"""
Integration tests for the ModelTrainer class and its linked modules.

These tests focus on the integration between ModelTrainer and its dependencies:
- src.train.callbacks (<PERSON><PERSON>, Callback<PERSON><PERSON><PERSON>, ModelRunCallback)
- src.utils.device (select_device)
- src.utils.logging (setup_training_logger)

The tests verify that components work together correctly in realistic scenarios.
"""

# pylint: disable=duplicate-code, protected-access

import logging
import tempfile
from dataclasses import dataclass
from pathlib import Path
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest
from torch import nn

from src.config.paths import (
    get_metrics_history_path,
    get_metrics_summary_path,
    get_run_dir,
)
from src.train.callbacks.early_stopping import EarlyStoppingCallback
from src.train.trainer import ModelTrainer
from src.train.trainer_config import DatabaseConfig, TrainerConfig
from tests.integration.trainer_test_utils import (
    ModelComponentsConfig,
    create_mock_database_callback_setup,
    create_test_data_loaders,
    create_test_model_components,
    create_test_training_config,
)


class SimpleTestModel(nn.Module):
    """A simple model for integration testing."""

    def __init__(self, input_size=10, hidden_size=5, output_size=1):
        super().__init__()
        self.linear1 = nn.Linear(input_size, hidden_size)
        self.relu = nn.ReLU()
        self.linear2 = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        """Forward pass of the model."""
        x = self.linear1(x)
        x = self.relu(x)
        x = self.linear2(x)
        return x


@pytest.fixture(name="integration_data_loaders")
def fixture_integration_data_loaders():
    """Create realistic data loaders for integration testing."""
    return create_test_data_loaders(
        train_samples=64,
        test_samples=32,
        input_size=10,
        batch_size=8,
        binary_classification=True,
    )


@pytest.fixture(name="integration_model_components")
def fixture_integration_model_components():
    """Create realistic model components for integration testing."""
    config = ModelComponentsConfig(
        input_size=10,
        hidden_size=5,
        output_size=1,
        binary_classification=True,
        optimizer_type="adam",
        learning_rate=0.001,
    )
    # Use real models for integration tests that need to save artifacts
    return create_test_model_components(config, use_mock=False)


@pytest.fixture(name="integration_training_config")
def fixture_integration_training_config():
    """Create training configuration for integration testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield create_test_training_config(
            temp_dir=temp_dir,
            model_id="integration_test_model",
            epochs=3,
            gradient_clip_max_norm=1.0,
            learning_rate_schedule=[
                {"epoch": 1, "rate": 0.0005},
                {"epoch": 2, "rate": 0.0001},
            ],
        )


@dataclass
class TrainerTestContext:
    """A context object to hold components for trainer integration tests."""

    model_components: dict
    data_loaders: dict
    training_config: dict
    tmp_path: Path
    monkeypatch: pytest.MonkeyPatch
    caplog: pytest.LogCaptureFixture


@pytest.fixture(name="trainer_test_context")
# pylint: disable=too-many-arguments, too-many-positional-arguments
def fixture_trainer_test_context(
    integration_model_components,
    integration_data_loaders,
    integration_training_config,
    tmp_path,
    monkeypatch,
    caplog,
) -> TrainerTestContext:
    """Create a complete context for trainer integration tests."""
    return TrainerTestContext(
        model_components=integration_model_components,
        data_loaders=integration_data_loaders,
        training_config=integration_training_config,
        tmp_path=tmp_path,
        monkeypatch=monkeypatch,
        caplog=caplog,
    )


class TestTrainerIntegration:
    """Integration tests for ModelTrainer with its dependencies."""

    def _setup_trainer(
        self, context: TrainerTestContext, callbacks=None
    ) -> tuple[ModelTrainer, dict]:
        """Helper to set up the ModelTrainer for tests."""
        context.monkeypatch.setattr("src.config.paths.RUNS_DIR", context.tmp_path)
        context.caplog.set_level(logging.INFO)

        training_config = context.training_config.copy()
        model_id = training_config["model_id"]
        # Use a test model_run_uuid for centralized paths
        model_run_uuid = f"test-{model_id}-uuid"
        training_config["model_run_uuid"] = model_run_uuid

        config = TrainerConfig(
            model_components=context.model_components,
            data_loaders=context.data_loaders,
            training_config=training_config,
            callbacks=callbacks or [],
        )
        return ModelTrainer(config), training_config

    def _verify_artifacts(self, training_config: dict):
        """Verify that training artifacts were saved correctly."""
        model_run_uuid = training_config["model_run_uuid"]

        # Verify model file using ModelPersistence structure
        model_checkpoint_path = get_run_dir(model_run_uuid) / "checkpoint.pt"
        assert (
            model_checkpoint_path.exists()
        ), f"Model checkpoint not found at {model_checkpoint_path}"

        # Verify metrics files using centralized paths
        history_path = get_metrics_history_path(model_run_uuid)
        summary_path = get_metrics_summary_path(model_run_uuid)

        assert history_path.exists(), f"History file not found at {history_path}"
        assert summary_path.exists(), f"Summary file not found at {summary_path}"

    def test_full_training_pipeline_integration(
        self, trainer_test_context: TrainerTestContext
    ):
        """Test complete training pipeline with all components integrated."""
        trainer, training_config = self._setup_trainer(trainer_test_context)

        # Verify initialization
        assert trainer.components.model is not None
        assert trainer.device is not None
        assert (
            next(trainer.components.model.parameters()).device.type
            == trainer.device.type
        )

        # Run training with mocked methods for speed
        metrics_callback = trainer.get_metrics_callback()
        with patch.object(trainer, "_train_epoch") as mock_train, patch.object(
            trainer, "_validate_epoch"
        ) as mock_validate, patch.object(metrics_callback, "_collect_resource_metrics"):
            mock_train.return_value = {"train_loss": 0.5, "train_accuracy": 0.8}
            mock_validate.return_value = {
                "validation_loss": 0.6,
                "validation_accuracy": 0.75,
            }
            metrics = trainer.train()

        # Verify metrics
        assert metrics is not None
        epochs = training_config["epochs"]
        assert len(metrics.train.losses) == epochs
        assert len(metrics.test.losses) == epochs
        assert "total_training_time" in metrics.timing

        # Verify artifacts
        self._verify_artifacts(training_config)

    def test_learning_rate_schedule_integration(
        self, trainer_test_context: TrainerTestContext
    ):
        """Test that learning rate schedule is properly integrated."""
        trainer, _ = self._setup_trainer(trainer_test_context)

        metrics_callback = trainer.get_metrics_callback()
        with patch.object(trainer, "_train_epoch") as mock_train, patch.object(
            trainer, "_validate_epoch"
        ) as mock_validate, patch.object(metrics_callback, "_collect_resource_metrics"):
            lr_changes = []
            initial_lr = trainer.components.optimizer.param_groups[0]["lr"]

            def track_lr(*args, **kwargs):  # pylint: disable=unused-argument
                lr_changes.append(trainer.components.optimizer.param_groups[0]["lr"])
                return {"train_loss": 0.5, "train_accuracy": 0.8}

            mock_train.side_effect = track_lr
            # Fix: Provide proper return value for _validate_epoch
            # to avoid mock serialization issues
            mock_validate.return_value = {
                "validation_loss": 0.6,
                "validation_accuracy": 0.75,
            }
            trainer.train()

            assert len(lr_changes) == 3
            assert lr_changes[0] == initial_lr
            assert lr_changes[1] == 0.0005
            assert lr_changes[2] == 0.0001

    def test_callback_system_integration(
        self, trainer_test_context: TrainerTestContext
    ):
        """Test integration of callback system with trainer."""
        early_stopping = EarlyStoppingCallback(monitor="validation_loss", patience=1)
        trainer, _ = self._setup_trainer(
            trainer_test_context, callbacks=[early_stopping]
        )

        side_effects = [
            {"validation_loss": loss, "validation_accuracy": 0.5}
            for loss in [0.8, 0.9, 1.0]
        ]
        with patch.object(
            trainer, "_validate_epoch", side_effect=side_effects
        ), patch.object(
            trainer,
            "_train_epoch",
            return_value={"train_loss": 0.5, "train_accuracy": 0.8},
        ), patch.object(
            trainer.get_metrics_callback(), "_collect_resource_metrics"
        ):
            metrics = trainer.train()
            assert trainer.stop_training is True
            assert len(metrics.train.losses) == 2

    def test_error_handling_integration(self, trainer_test_context: TrainerTestContext):
        """Test error handling and artifact saving on failure."""
        # Test RuntimeError
        trainer, training_config = self._setup_trainer(trainer_test_context)
        with patch.object(
            trainer, "_train_epoch", side_effect=RuntimeError("Simulated error")
        ):
            with pytest.raises(RuntimeError):
                trainer.train()
            assert trainer.metrics.error["type"] == "RuntimeError"
            self._verify_artifacts(training_config)

        # Test KeyboardInterrupt
        trainer, training_config = self._setup_trainer(trainer_test_context)
        with patch.object(trainer, "_train_epoch", side_effect=KeyboardInterrupt):
            trainer.train()  # Should not raise
            assert trainer.metrics.error["type"] == "KeyboardInterrupt"
            self._verify_artifacts(training_config)


class TestTrainerDatabaseIntegration:
    """Integration tests for ModelTrainer with database components."""

    @patch("src.train.trainer.ModelRunCallback")
    def test_database_callback_integration(
        self, mock_callback_class, trainer_test_context: TrainerTestContext
    ):
        """Test integration with ModelRunCallback for database updates."""
        mock_instance, model_run_uuid, db_config = create_mock_database_callback_setup()
        mock_callback_class.return_value = mock_instance

        context = trainer_test_context
        context.training_config["database"] = db_config

        config = TrainerConfig(
            model_components=context.model_components,
            data_loaders=context.data_loaders,
            training_config=context.training_config,
            database_config=db_config,
        )
        trainer = ModelTrainer(config)

        mock_callback_class.assert_called_once_with(
            model_run_uuid=model_run_uuid, profile="development", verbose=True
        )
        assert mock_instance in trainer.callback_handler.callbacks

        # Mock training methods to focus on callback integration
        with patch.object(
            trainer,
            "_train_epoch",
            return_value={"train_loss": 0.5, "train_accuracy": 0.8},
        ), patch.object(
            trainer,
            "_validate_epoch",
            return_value={"validation_loss": 0.6, "validation_accuracy": 0.75},
        ), patch.object(
            trainer.get_metrics_callback(), "_collect_resource_metrics"
        ):
            trainer.train()

            # Verify callback lifecycle methods were called
            mock_instance.on_train_begin.assert_called_once()
            mock_instance.on_train_end.assert_called_once()
            assert (
                mock_instance.on_epoch_end.call_count
                == context.training_config["epochs"]
            )

    @patch("src.train.trainer.ModelRunCallback")
    def test_database_callback_import_error_handling(
        self,
        mock_callback_class,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test handling of ModelRunCallback import errors."""
        # Simulate import error
        mock_callback_class.side_effect = ImportError("Database service not available")

        database_config = DatabaseConfig(
            model_run_uuid=str(uuid4()), profile="development"
        )

        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
            database_config=database_config,
        )

        # Mock the logger to verify warning is logged
        with patch("src.train.trainer.setup_training_logger") as mock_setup_logger:
            mock_logger = MagicMock()
            mock_setup_logger.return_value = mock_logger

            trainer = ModelTrainer(config)

            # Verify warning was logged about failed import
            mock_logger.warning.assert_called_once()
            warning_call = mock_logger.warning.call_args[0][0]
            assert "Failed to import ModelRunCallback" in warning_call

            # Verify trainer still works without database callback
            # (but has MetricsCollectionCallback, TestImageVisualizationCallback,
            # and MetricsPlottingCallback)
            assert len(trainer.callback_handler.callbacks) == 3
            callback_names = [
                cb.__class__.__name__ for cb in trainer.callback_handler.callbacks
            ]
            assert "MetricsCollectionCallback" in callback_names
            assert "TestImageVisualizationCallback" in callback_names

    def test_database_config_none_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test trainer works correctly without database configuration."""
        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
            database_config=None,
        )

        trainer = ModelTrainer(config)

        # Verify no database callbacks were added
        # (but MetricsCollectionCallback, TestImageVisualizationCallback,
        # and MetricsPlottingCallback are auto-added)
        assert len(trainer.callback_handler.callbacks) == 3
        callback_names = [
            cb.__class__.__name__ for cb in trainer.callback_handler.callbacks
        ]
        assert "MetricsCollectionCallback" in callback_names
        assert "TestImageVisualizationCallback" in callback_names

        # Verify training still works with mocked methods for speed
        metrics_callback = trainer.get_metrics_callback()
        with patch.object(trainer, "_train_epoch") as mock_train, patch.object(
            trainer, "_validate_epoch"
        ) as mock_validate, patch.object(metrics_callback, "_collect_resource_metrics"):
            mock_train.return_value = {"train_loss": 0.5, "train_accuracy": 0.8}
            mock_validate.return_value = {
                "validation_loss": 0.6,
                "validation_accuracy": 0.75,
            }
            metrics = trainer.train()

        assert metrics is not None
        assert len(metrics.train.losses) == integration_training_config["epochs"]

    def test_database_config_partial_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test database config with missing model_run_uuid."""
        # Database config without model_run_uuid
        database_config = DatabaseConfig(model_run_uuid=None, profile="development")

        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
            database_config=database_config,
        )

        trainer = ModelTrainer(config)

        # Verify no database callbacks were added when UUID is None
        # (but MetricsCollectionCallback, TestImageVisualizationCallback,
        # and MetricsPlottingCallback are auto-added)
        assert len(trainer.callback_handler.callbacks) == 3
        callback_names = [
            cb.__class__.__name__ for cb in trainer.callback_handler.callbacks
        ]
        assert "MetricsCollectionCallback" in callback_names
        assert "TestImageVisualizationCallback" in callback_names


class TestTrainerConfigIntegration:
    """Integration tests for the ModelTrainer with TrainerConfig."""

    def test_config_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test direct TrainerConfig usage."""

        # Create config using the dataclass
        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
        )

        # Create trainer using direct config
        trainer = ModelTrainer(config)

        # Verify trainer was created correctly
        assert trainer.components.model == integration_model_components["model"]
        assert trainer.data_loaders == integration_data_loaders
        assert trainer.config.config == integration_training_config

        # Verify training works with mocked methods for speed
        metrics_callback = trainer.get_metrics_callback()
        with patch.object(trainer, "_train_epoch") as mock_train, patch.object(
            trainer, "_validate_epoch"
        ) as mock_validate, patch.object(metrics_callback, "_collect_resource_metrics"):
            mock_train.return_value = {"train_loss": 0.5, "train_accuracy": 0.8}
            mock_validate.return_value = {
                "validation_loss": 0.6,
                "validation_accuracy": 0.75,
            }
            metrics = trainer.train()

        assert metrics is not None
        assert len(metrics.train.losses) == integration_training_config["epochs"]

    def test_config_with_database_config(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test TrainerConfig with database configuration."""

        database_config = DatabaseConfig(model_run_uuid=str(uuid4()), profile="staging")

        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
            database_config=database_config,
        )

        with patch("src.train.trainer.ModelRunCallback") as mock_callback_class:
            mock_callback_instance = MagicMock()
            mock_callback_class.return_value = mock_callback_instance

            trainer = ModelTrainer(config)

            # Verify database callback was integrated
            mock_callback_class.assert_called_once()
            assert mock_callback_instance in trainer.callback_handler.callbacks


# pylint: enable=duplicate-code
