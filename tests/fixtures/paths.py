"""
Path and directory isolation fixtures for tests.

This module provides fixtures for isolating test runs and datasets
to prevent interference with production data.
"""

import shutil
import tempfile
from pathlib import Path

import pytest

from src.config.paths import DatasetConfig, RunsConfig, get_dataset_dir, get_run_dir


@pytest.fixture(scope="session")
def test_runs_dir():
    """Create a temporary directory for test runs that gets cleaned up after all tests."""
    with tempfile.TemporaryDirectory(prefix="test_runs_") as temp_dir:
        yield temp_dir


@pytest.fixture(scope="session")
def test_datasets_dir():
    """Create a temporary directory for test datasets that gets cleaned up after all tests."""
    with tempfile.TemporaryDirectory(prefix="test_datasets_") as temp_dir:
        yield temp_dir


@pytest.fixture(autouse=True)
def isolate_runs_directory(request, monkeypatch):
    """Automatically isolate all tests to use a temporary runs directory."""
    temp_runs_path = request.getfixturevalue("test_runs_dir")
    # Convert to Path object to maintain type consistency
    temp_runs_path = Path(temp_runs_path)

    # Patch the RUNS_DIR constant in src.config.paths (for backward compatibility)
    monkeypatch.setattr("src.config.paths.RUNS_DIR", temp_runs_path)

    # CRITICAL: Patch the _runs_config global instance to use the temporary directory
    # This is the main fix - the new RunsConfig system uses this global instance
    temp_runs_config = RunsConfig()
    temp_runs_config.set_base_dir(temp_runs_path)
    monkeypatch.setattr("src.config.paths._runs_config", temp_runs_config)

    # Also patch the config.paths module (which is how some modules import it)
    monkeypatch.setattr("config.paths._runs_config", temp_runs_config)


@pytest.fixture(autouse=True)
def isolate_datasets_directory(request, monkeypatch):
    """Automatically isolate all tests to use a temporary datasets directory."""
    temp_datasets_path = request.getfixturevalue("test_datasets_dir")
    # Convert to Path object to maintain type consistency
    temp_datasets_path = Path(temp_datasets_path)

    # Patch the DATASETS_BASE_DIR constant in src.config.paths
    monkeypatch.setattr("src.config.paths.DATASETS_BASE_DIR", temp_datasets_path)

    # Create a new DatasetConfig instance with the temporary directory
    temp_config = DatasetConfig()
    temp_config.set_base_dir(temp_datasets_path)

    # Patch the global dataset config instance in the module
    monkeypatch.setattr("src.config.paths._dataset_config", temp_config)

    # Also patch the config.paths module (which is how tests import it)
    monkeypatch.setattr("config.paths._dataset_config", temp_config)


@pytest.fixture
def cleanup_test_runs():
    """
    Fixture to ensure test run directories are cleaned up after individual tests.

    This fixture can be used by tests that create specific run directories
    and want to ensure they're cleaned up even if the test fails.

    Usage:
        def test_something(cleanup_test_runs):
            model_run_uuid = "test-uuid-123"
            cleanup_test_runs.add_run_uuid(model_run_uuid)
            # ... test code that creates directories ...
            # Cleanup happens automatically after test
    """

    run_uuids_to_cleanup = []

    class CleanupHelper:
        """Helper class to add run UUIDs to be cleaned up."""

        def add_run_uuid(self, model_run_uuid: str):
            """Add a model run UUID to be cleaned up after the test."""
            run_uuids_to_cleanup.append(model_run_uuid)

    helper = CleanupHelper()
    yield helper

    # Cleanup after test completes
    for uuid in run_uuids_to_cleanup:
        # Use the patched RUNS_DIR from the isolation fixture
        run_dir = Path(get_run_dir(uuid))
        if run_dir.exists():
            shutil.rmtree(run_dir, ignore_errors=True)


@pytest.fixture
def cleanup_test_datasets():
    """
    Fixture to ensure test dataset directories are cleaned up after individual tests.

    This fixture can be used by tests that create specific dataset directories
    and want to ensure they're cleaned up even if the test fails.

    Usage:
        def test_something(cleanup_test_datasets):
            dataset_uuid = "test-uuid-123"
            cleanup_test_datasets.add_dataset_uuid(dataset_uuid)
            # ... test code that creates directories ...
            # Cleanup happens automatically after test
    """

    dataset_uuids_to_cleanup = []

    class CleanupHelper:
        """Helper class to add dataset UUIDs to be cleaned up."""

        def add_dataset_uuid(self, dataset_uuid: str):
            """Add a dataset UUID to be cleaned up after the test."""
            dataset_uuids_to_cleanup.append(dataset_uuid)

    helper = CleanupHelper()
    yield helper

    # Cleanup after test completes
    for uuid in dataset_uuids_to_cleanup:
        # Use the configurable dataset config to get the correct base directory
        # Try to clean up with and without timestamp since tests might use either
        dataset_dir = get_dataset_dir(uuid)
        if dataset_dir.exists():
            shutil.rmtree(dataset_dir, ignore_errors=True)

        # Also try with a common test timestamp
        dataset_dir_with_timestamp = get_dataset_dir(uuid, "2025-01-01 12:00:00+00")
        if dataset_dir_with_timestamp.exists():
            shutil.rmtree(dataset_dir_with_timestamp, ignore_errors=True)
