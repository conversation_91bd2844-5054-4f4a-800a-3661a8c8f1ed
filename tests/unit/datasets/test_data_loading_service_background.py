"""
Tests for background class functionality in DataLoadingService.

This module tests the integration of background class support in the data loading service.
"""

from unittest.mock import patch
from uuid import uuid4

from config.paths import get_background_dir_name
from datasets.data_loading_service import (
    BackgroundClassConfig,
    DataLoaderConfig,
    DataLoadingService,
    DataProcessingConfig,
    DataSplitConfig,
    ImageDatasetParams,
)
from tests.fixtures.data import create_dataset_sets_for_background_tests


class TestDataLoadingServiceBackground:
    """Test background class functionality in DataLoadingService."""

    def test_data_loader_config_background_defaults(self):
        """Test that DataLoaderConfig has correct background class defaults."""
        config = DataLoaderConfig(model_run_uuid="test-uuid-v0")

        assert config.background_config.include_background_class is True
        assert config.background_config.background_class_name == "background"

    def test_data_loader_config_background_customization(self):
        """Test that background class settings can be customized."""
        background_config = BackgroundClassConfig(
            include_background_class=False, background_class_name="no_object"
        )
        config = DataLoaderConfig(
            model_run_uuid="test-uuid-v1",
            background_config=background_config,
        )

        assert config.background_config.include_background_class is False
        assert config.background_config.background_class_name == "no_object"

    def test_data_loader_config_new_structure(self):
        """Test that the new configuration structure works correctly."""
        # Test using new grouped config objects
        split_config = DataSplitConfig(
            test_size=0.3, validation_size=0.15, include_validation=False
        )
        processing_config = DataProcessingConfig(
            batch_size=64, dataset_batch_size=500, use_mock_data=True
        )

        config = DataLoaderConfig(
            model_run_uuid="test-uuid-new",
            split_config=split_config,
            processing_config=processing_config,
        )

        # Verify the new structure
        assert config.split_config.test_size == 0.3
        assert config.split_config.validation_size == 0.15
        assert config.split_config.include_validation is False
        assert config.processing_config.batch_size == 64
        assert config.processing_config.dataset_batch_size == 500
        assert config.processing_config.use_mock_data is True

        # Verify that the same values are accessible through the config objects
        assert config.split_config.test_size == 0.3
        assert config.split_config.validation_size == 0.15
        assert config.split_config.include_validation is False
        assert config.processing_config.batch_size == 64
        assert config.processing_config.dataset_batch_size == 500
        assert config.processing_config.use_mock_data is True

    def test_data_loader_config_direct_access(self):
        """Test that the new configuration structure allows direct access to nested configs."""
        # Test using new grouped config objects
        split_config = DataSplitConfig(
            test_size=0.3, validation_size=0.15, include_validation=False
        )
        processing_config = DataProcessingConfig(
            batch_size=64, dataset_batch_size=500, use_mock_data=True
        )

        config = DataLoaderConfig(
            model_run_uuid="test-uuid-direct",
            split_config=split_config,
            processing_config=processing_config,
        )

        # Verify direct access to nested configs
        assert config.split_config.test_size == 0.3
        assert config.split_config.validation_size == 0.15
        assert config.split_config.include_validation is False
        assert config.processing_config.batch_size == 64
        assert config.processing_config.dataset_batch_size == 500
        assert config.processing_config.use_mock_data is True

    def test_create_image_dataset_passes_background_config(self, tmp_path):
        """Test that _create_image_dataset passes background class config to ImageDataset."""
        # Create dataset sets using fixture
        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid, labeled_count=1, background_count=1
        )

        # Create config with custom background settings
        background_config = BackgroundClassConfig(
            include_background_class=True, background_class_name="no_object"
        )
        config = DataLoaderConfig(
            model_run_uuid="test-uuid-v2",
            background_config=background_config,
        )

        # Create dataset
        params = ImageDatasetParams(
            dataset_sets=dataset_sets,
            dataset_uuid=str(uuid4()),
            content_updated_at=None,
            set_name="test",
            config=config,
            base_dir=str(tmp_path),
        )
        # pylint: disable=protected-access
        dataset = DataLoadingService._create_image_dataset(params)

        # Verify that the dataset was created with correct background settings
        assert dataset.config.include_background_class is True
        assert dataset.config.background_class_name == "no_object"

        # Verify that background class is in the label mapping
        label_mapping = dataset.get_label_mapping()
        assert "no_object" in label_mapping

    def test_create_image_dataset_background_disabled(self, tmp_path):
        """Test that background images are filtered out when background class is disabled."""
        # Create dataset sets using fixture
        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=1,
            background_count=1,
            image_urls=[
                "http://example.com/labeled.jpg",
                "http://example.com/background.jpg",
            ],
        )

        # Create config with background class disabled
        background_config = BackgroundClassConfig(include_background_class=False)
        config = DataLoaderConfig(
            model_run_uuid="test-uuid-v3", background_config=background_config
        )

        # Create dataset
        params = ImageDatasetParams(
            dataset_sets=dataset_sets,
            dataset_uuid=str(uuid4()),
            content_updated_at=None,
            set_name="test",
            config=config,
            base_dir=str(tmp_path),
        )
        # pylint: disable=protected-access
        dataset = DataLoadingService._create_image_dataset(params)

        # Verify that only labeled images are included
        assert len(dataset) == 1
        assert dataset.get_num_classes() == 1

        # Verify that background class is not in the label mapping
        label_mapping = dataset.get_label_mapping()
        assert "background" not in label_mapping

    @patch("datasets.data_loading_service.get_dataset_images_dir")
    def test_image_path_construction_for_background_images(
        self, mock_get_dir, tmp_path
    ):
        """Test that image paths are constructed correctly for background images."""
        mock_get_dir.return_value = tmp_path / "images"

        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=0,
            background_count=1,
            image_urls=["http://example.com/test_bg_image.jpg"],
        )
        # Override image_uuid for this specific test
        dataset_sets[0].image_uuid = "test_bg_image"

        background_config = BackgroundClassConfig(
            include_background_class=True,
            background_class_name=get_background_dir_name(),
        )
        config = DataLoaderConfig(
            model_run_uuid="test-uuid-v4",
            background_config=background_config,
        )

        params = ImageDatasetParams(
            dataset_sets=dataset_sets,
            dataset_uuid=str(uuid4()),
            content_updated_at=None,
            set_name="test",
            config=config,
        )
        # pylint: disable=protected-access
        dataset = DataLoadingService._create_image_dataset(params)

        # Verify that the background image path is constructed correctly
        expected_path = (
            tmp_path / "images" / get_background_dir_name() / "test_bg_image.jpg"
        )
        assert dataset.image_paths[0] == expected_path

    def test_logging_includes_background_class_status(self, tmp_path, caplog):
        """Test that logging includes information about background class status."""
        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid, labeled_count=1, background_count=0
        )

        background_config = BackgroundClassConfig(include_background_class=True)
        config = DataLoaderConfig(
            model_run_uuid="test-uuid-v5", background_config=background_config
        )

        with caplog.at_level("INFO"):
            params = ImageDatasetParams(
                dataset_sets=dataset_sets,
                dataset_uuid=str(uuid4()),
                content_updated_at=None,
                set_name="test",
                config=config,
                base_dir=str(tmp_path),
            )
            # pylint: disable=protected-access
            DataLoadingService._create_image_dataset(params)

        # Check that log message includes background class status
        log_messages = [record.message for record in caplog.records]
        background_log = next(
            (msg for msg in log_messages if "background class" in msg), None
        )
        assert background_log is not None
        assert "enabled" in background_log

    def test_logging_background_class_disabled(self, tmp_path, caplog):
        """Test logging when background class is disabled."""
        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid, labeled_count=1, background_count=0
        )

        background_config = BackgroundClassConfig(include_background_class=False)
        config = DataLoaderConfig(
            model_run_uuid="test-uuid-v6", background_config=background_config
        )

        with caplog.at_level("INFO"):
            params = ImageDatasetParams(
                dataset_sets=dataset_sets,
                dataset_uuid=str(uuid4()),
                content_updated_at=None,
                set_name="test",
                config=config,
                base_dir=str(tmp_path),
            )
            # pylint: disable=protected-access
            DataLoadingService._create_image_dataset(params)

        # Check that log message includes background class status
        log_messages = [record.message for record in caplog.records]
        background_log = next(
            (msg for msg in log_messages if "background class" in msg), None
        )
        assert background_log is not None
        assert "disabled" in background_log
