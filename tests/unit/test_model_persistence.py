"""
Tests for the ModelPersistence class in src.models.persistence module.
"""

import json
import tempfile
from pathlib import Path

import pytest
import torch
from torch import nn, optim

from src.models.persistence import LoadModelOptions, ModelData, ModelPersistence


class SimpleModel(nn.Module):
    """A simple model for testing."""

    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(10, 1)

    def forward(self, x):
        """Forward pass of the model."""
        return self.linear(x)


@pytest.fixture
def model():
    """Create a simple model for testing."""
    return SimpleModel()


@pytest.fixture
def optimizer(request):
    """Create an optimizer for testing."""
    model_instance = request.getfixturevalue("model")
    return optim.SGD(model_instance.parameters(), lr=0.01)


@pytest.fixture
def metadata():
    """Create metadata for testing."""
    return {
        "accuracy": 0.85,
        "loss": 0.15,
        "epochs": 10,
    }


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir_path:
        yield temp_dir_path


@pytest.fixture
def persistence(request):
    """Create a ModelPersistence instance for testing."""
    temp_dir_path = request.getfixturevalue("temp_dir")
    return ModelPersistence(base_dir=temp_dir_path)


class TestModelPersistence:
    """Tests for the ModelPersistence class."""

    def test_initialization(self, request):
        """Test initialization of ModelPersistence."""
        temp_dir_path = request.getfixturevalue("temp_dir")
        test_persistence = ModelPersistence(base_dir=temp_dir_path)
        assert test_persistence.base_dir == Path(temp_dir_path)
        assert test_persistence.base_dir.exists()

    def test_initialization_default(self):
        """Test initialization with default base_dir."""
        # This test now uses the isolated test runs directory via the autouse fixture
        test_persistence = ModelPersistence()
        assert test_persistence.base_dir.exists()
        # The base_dir should now point to our temporary test directory
        assert "test_runs_" in str(test_persistence.base_dir)

    def test_save_model(self, request):
        """Test saving a model."""
        test_persistence = request.getfixturevalue("persistence")
        test_model = request.getfixturevalue("model")
        test_optimizer = request.getfixturevalue("optimizer")
        test_metadata = request.getfixturevalue("metadata")

        model_data = ModelData(
            model=test_model, optimizer=test_optimizer, metadata=test_metadata
        )
        model_dir = test_persistence.save_model(model_data, "test_model")

        # Check that files were created
        model_dir_path = Path(model_dir)
        assert (model_dir_path / "checkpoint.pt").exists()
        assert (model_dir_path / "metadata.json").exists()

        # Check metadata was saved correctly
        with open(model_dir_path / "metadata.json", "r", encoding="utf-8") as f:
            saved_metadata = json.load(f)
        assert saved_metadata == test_metadata

    def test_save_model_without_optimizer(self, request):
        """Test saving a model without an optimizer."""
        test_persistence = request.getfixturevalue("persistence")
        test_model = request.getfixturevalue("model")
        test_metadata = request.getfixturevalue("metadata")

        model_data = ModelData(model=test_model, metadata=test_metadata)
        model_dir = test_persistence.save_model(model_data, "test_model_no_opt")

        # Check that files were created
        model_dir_path = Path(model_dir)
        assert (model_dir_path / "checkpoint.pt").exists()

        # Load the checkpoint to verify optimizer wasn't saved
        checkpoint = torch.load(model_dir_path / "checkpoint.pt")
        assert "model_state_dict" in checkpoint
        assert "optimizer_state_dict" not in checkpoint

    def test_save_model_without_metadata(self, request):
        """Test saving a model without metadata."""
        test_persistence = request.getfixturevalue("persistence")
        test_model = request.getfixturevalue("model")
        test_optimizer = request.getfixturevalue("optimizer")

        model_data = ModelData(model=test_model, optimizer=test_optimizer)
        model_dir = test_persistence.save_model(model_data, "test_model_no_meta")

        # Check that checkpoint was created but not metadata file
        model_dir_path = Path(model_dir)
        assert (model_dir_path / "checkpoint.pt").exists()
        assert not (model_dir_path / "metadata.json").exists()

    def test_load_model(self, request):
        """Test loading a model."""
        test_persistence = request.getfixturevalue("persistence")
        test_model = request.getfixturevalue("model")
        test_optimizer = request.getfixturevalue("optimizer")
        test_metadata = request.getfixturevalue("metadata")

        # First save a model
        model_data = ModelData(
            model=test_model, optimizer=test_optimizer, metadata=test_metadata
        )
        test_persistence.save_model(model_data, "test_model_load")

        # Create a new model and optimizer for loading
        new_model = SimpleModel()
        new_optimizer = optim.SGD(
            new_model.parameters(), lr=0.02
        )  # Different learning rate

        # Load the model
        load_options = LoadModelOptions(model=new_model, optimizer=new_optimizer)
        loaded_model, loaded_optimizer, loaded_metadata = test_persistence.load_model(
            load_options, "test_model_load"
        )

        # Check that the model was loaded correctly
        assert loaded_model is new_model  # Should be the same object
        assert loaded_optimizer is new_optimizer  # Should be the same object
        assert loaded_metadata == test_metadata

        # Check that the optimizer parameters were updated
        assert (
            loaded_optimizer.param_groups[0]["lr"] == 0.01
        )  # Should be updated to saved value

    def test_load_model_without_optimizer(self, request):
        """Test loading a model without an optimizer."""
        test_persistence = request.getfixturevalue("persistence")
        test_model = request.getfixturevalue("model")
        test_metadata = request.getfixturevalue("metadata")

        # First save a model
        model_data = ModelData(model=test_model, metadata=test_metadata)
        test_persistence.save_model(model_data, "test_model_load_no_opt")

        # Create a new model for loading
        new_model = SimpleModel()

        # Load the model
        load_options = LoadModelOptions(model=new_model)
        loaded_model, loaded_optimizer, loaded_metadata = test_persistence.load_model(
            load_options, "test_model_load_no_opt"
        )

        # Check that the model was loaded correctly
        assert loaded_model is new_model  # Should be the same object
        assert loaded_optimizer is None  # No optimizer was provided
        assert loaded_metadata == test_metadata

    def test_load_nonexistent_model(self, request):
        """Test loading a model that doesn't exist."""
        test_persistence = request.getfixturevalue("persistence")
        test_model = request.getfixturevalue("model")

        load_options = LoadModelOptions(model=test_model)
        with pytest.raises(FileNotFoundError):
            test_persistence.load_model(load_options, "nonexistent_model")

    def test_discard_model(self, request):
        """Test discarding a model."""
        test_persistence = request.getfixturevalue("persistence")
        test_model = request.getfixturevalue("model")
        test_optimizer = request.getfixturevalue("optimizer")

        # First save a model
        model_data = ModelData(model=test_model, optimizer=test_optimizer)
        test_persistence.save_model(model_data, "test_model_discard")

        # Check that the model directory exists
        model_dir = test_persistence.base_dir / "test_model_discard"
        assert model_dir.exists()

        # Discard the model
        result = test_persistence.discard_model("test_model_discard")
        assert result is True
        assert not model_dir.exists()

    def test_discard_nonexistent_model(self, request):
        """Test discarding a model that doesn't exist."""
        test_persistence = request.getfixturevalue("persistence")
        result = test_persistence.discard_model("nonexistent_model")
        assert result is False

    def test_list_models(self, request):
        """Test listing models."""
        test_persistence = request.getfixturevalue("persistence")
        test_model = request.getfixturevalue("model")

        # Initially there should be no models
        assert test_persistence.list_models() == []

        # Save some models
        for i in range(3):
            model_data = ModelData(model=test_model)
            test_persistence.save_model(model_data, f"test_model_{i}")

        # Check that the models are listed
        models = test_persistence.list_models()
        assert len(models) == 3
        assert "test_model_0" in models
        assert "test_model_1" in models
        assert "test_model_2" in models
