"""
Tests for the MLModelFactory class in src.models.ml_factory module.
"""

import tempfile
from pathlib import Path

import pytest
import torch
from torch import nn, optim

from src.models.ml_factory import MLModelFactory
from src.models.persistence import ModelPersistence


class SimpleModel(nn.Module):
    """A simple model for testing."""

    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(10, 1)

    def forward(self, x):
        """Forward pass of the model."""
        return self.linear(x)


@pytest.fixture
def temp_dir_path():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield tmp_dir


@pytest.fixture
def ml_factory(request):
    """Create an MLModelFactory instance for testing."""
    temp_dir = request.getfixturevalue("temp_dir_path")
    return MLModelFactory(persistence_base_dir=temp_dir)


class TestMLModelFactory:
    """Tests for the MLModelFactory class."""

    def test_initialization(self, request):
        """Test initialization of MLModelFactory."""
        # Get the fixture value from the request fixture
        temp_dir = request.getfixturevalue("temp_dir_path")
        factory = MLModelFactory(persistence_base_dir=temp_dir)
        assert isinstance(factory.persistence, ModelPersistence)
        assert factory.persistence.base_dir == Path(temp_dir)

    def test_initialization_default(self):
        """Test initialization with default persistence_base_dir."""
        # This test now uses the isolated test runs directory via the autouse fixture
        factory = MLModelFactory()
        assert isinstance(factory.persistence, ModelPersistence)
        assert factory.persistence.base_dir.exists()
        # The base_dir should now point to our temporary test directory
        assert "test_runs_" in str(factory.persistence.base_dir)

    def test_create_loss_function(self, request):
        """Test creating a loss function."""
        # Get the fixture value from the request fixture
        factory = request.getfixturevalue("ml_factory")
        loss_params = {"name": "BCEWithLogitsLoss"}
        loss_fn = factory.create_loss_function(loss_params)
        assert isinstance(loss_fn, nn.BCEWithLogitsLoss)

    def test_create_optimizer(self, request):
        """Test creating an optimizer."""
        # Get the fixture value from the request fixture
        factory = request.getfixturevalue("ml_factory")
        model = SimpleModel()
        optimizer_params = {"name": "Adam", "lr": 0.001}
        optimizer = factory.create_optimizer(optimizer_params, model.parameters())
        assert isinstance(optimizer, optim.Adam)
        assert optimizer.param_groups[0]["lr"] == 0.001

    def test_create_scheduler(self, request):
        """Test creating a scheduler."""
        # Get the fixture value from the request fixture
        factory = request.getfixturevalue("ml_factory")
        model = SimpleModel()
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        scheduler_params = {"type": "step", "step_size": 10, "gamma": 0.1}
        scheduler = factory.create_scheduler(scheduler_params, optimizer)
        assert isinstance(scheduler, torch.optim.lr_scheduler.StepLR)
        assert scheduler.step_size == 10
        assert scheduler.gamma == 0.1
