# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# project directories
.env/
src/data/
tests/manual/output/
*.egg-info/

# Environment files
.env
.env.*
!.environment.example

# Virtual environment
.env/

# Model training runs
runs/

# Python bytecode and build artifacts
__pycache__/
build/

# Test artifacts
.pytest_cache/
htmlcov/
.coverage

# IDE specific
.vscode/
