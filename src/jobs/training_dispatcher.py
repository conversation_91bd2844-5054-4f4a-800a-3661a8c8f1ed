"""
Training job dispatcher implementation.

This module provides the TrainingJobDispatcher class for scheduling and
executing machine learning model training jobs using the ModelTrainer.
"""

import asyncio
import logging
from asyncio import PriorityQueue
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

import psutil
import torch

from augmentations import AugmentationUtils
from augmentations.factory import AugmentationPipelineFactory
from config.paths import get_dataset_images_dir
from database.services.dataset_service import DatasetService
from database.services.dataset_state_cache import DatasetStateCacheService
from database.services.training_data_service import TrainingDataService
from datasets.data_loading_service import (
    DataLoaderConfig,
    DataLoadingService,
    DataProcessingConfig,
    DataSplitConfig,
)
from jobs.base import Job, JobCallback, JobDispatcher, JobPriority, JobStatus
from jobs.callbacks.database import DatabaseUpdateCallback
from jobs.callbacks.resource_monitor import ResourceMonitorCallback
from jobs.training_data import TrainingJob, TrainingJobData
from src.database.services.model_run_service import (
    ModelRunService,
    ModelRunTimingUpdate,
)
from src.train.callbacks.model_run import ModelRunCallback
from src.train.trainer import ModelTrainer
from src.train.trainer_config import DatabaseConfig, TrainerConfig
from utils.async_utils import maybe_await

logger = logging.getLogger(__name__)


@dataclass
class DispatcherConfig:
    """Configuration for the TrainingJobDispatcher."""

    # Maximum number of concurrent jobs
    max_concurrent_jobs: int = 1

    # Maximum number of jobs in the queue
    max_queue_size: int = 100

    # Whether to automatically start processing jobs
    auto_start: bool = True

    # Polling interval for checking database for new jobs (seconds)
    polling_interval: float = 60.0

    # Database profile to use
    profile: Optional[str] = None

    # Resource limits
    max_cpu_usage_percent: float = 90.0
    max_memory_usage_percent: float = 90.0
    max_gpu_memory_usage_percent: float = 90.0


class TrainingJobDispatcher(JobDispatcher):
    """
    Dispatcher for machine learning model training jobs.

    This class is responsible for:
    1. Fetching pending training jobs from the database
    2. Prioritizing jobs based on configurable criteria
    3. Allocating resources for each job
    4. Launching ModelTrainer instances for each job
    5. Monitoring running jobs and handling failures
    6. Supporting concurrent execution of multiple training jobs
    """

    def __init__(
        self,
        config: Optional[DispatcherConfig] = None,
        callbacks: Optional[List[JobCallback]] = None,
    ):
        """
        Initialize the training job dispatcher.

        Args:
            config: Configuration for the dispatcher
            callbacks: List of callbacks to be notified of job events
        """
        # Initialize with default callbacks if none provided
        default_callbacks = []

        # Add default database update callback if not already provided
        if not any(isinstance(cb, DatabaseUpdateCallback) for cb in (callbacks or [])):
            default_callbacks.append(DatabaseUpdateCallback())

        # Add default resource monitor callback if not already provided
        if not any(isinstance(cb, ResourceMonitorCallback) for cb in (callbacks or [])):
            default_callbacks.append(ResourceMonitorCallback())

        all_callbacks = (callbacks or []) + default_callbacks

        super().__init__(callbacks=all_callbacks)

        self.config = config or DispatcherConfig()
        self.job_queue: PriorityQueue = PriorityQueue(
            maxsize=self.config.max_queue_size
        )
        self.running_jobs: Dict[str, asyncio.Task] = {}
        self.completed_jobs: Dict[str, Job] = {}
        self._polling_task: Optional[asyncio.Task] = None
        self._processing_task: Optional[asyncio.Task] = None

        # If auto_start is enabled, start the dispatcher
        if self.config.auto_start:
            asyncio.create_task(self.start())

    async def start(self) -> None:
        """Start the dispatcher and begin processing jobs."""
        await super().start()
        logger.info("Starting TrainingJobDispatcher")

        # Start polling for new jobs
        self._polling_task = asyncio.create_task(self._poll_for_jobs())

        # Start processing jobs from the queue
        self._processing_task = asyncio.create_task(self._process_queue())

    async def stop(self) -> None:
        """Stop the dispatcher and cancel all running jobs."""
        logger.info("Stopping TrainingJobDispatcher")

        # Cancel polling task
        if self._polling_task:
            self._polling_task.cancel()
            self._polling_task = None

        # Cancel processing task
        if self._processing_task:
            self._processing_task.cancel()
            self._processing_task = None

        # Cancel all running jobs
        for job_id, task in list(self.running_jobs.items()):
            logger.info("Cancelling job %s", job_id)
            task.cancel()

        # Wait for all tasks to complete
        if self.running_jobs:
            await asyncio.gather(*self.running_jobs.values(), return_exceptions=True)

        self.running_jobs.clear()
        await super().stop()

    async def schedule_job(self, job: Job) -> None:
        """
        Schedule a job for execution.

        Args:
            job: The job to schedule
        """
        if not isinstance(job, TrainingJob):
            raise ValueError("Only TrainingJob instances can be scheduled")

        logger.info("Scheduling job %s", job.job_id)
        job.status = JobStatus.SCHEDULED
        self.jobs[job.job_id] = job

        # Notify callbacks
        self._notify_callbacks("on_job_scheduled", job)

        # Add job to queue
        await self.job_queue.put(job)

    async def cancel_job(self, job_id: str) -> bool:
        """
        Cancel a scheduled or running job.

        Args:
            job_id: ID of the job to cancel

        Returns:
            True if the job was cancelled, False otherwise
        """
        # Check if job exists
        job = self.jobs.get(job_id)
        if not job:
            logger.warning("Cannot cancel job %s: Job not found", job_id)
            return False

        # If job is running, cancel the task
        task = self.running_jobs.get(job_id)
        if task:
            logger.info("Cancelling running job %s", job_id)
            task.cancel()
            job.status = JobStatus.CANCELLED
            self._notify_callbacks("on_job_cancelled", job)
            return True

        # If job is scheduled but not running, mark as cancelled
        if job.status in (JobStatus.SCHEDULED, JobStatus.PENDING):
            logger.info("Cancelling scheduled job %s", job_id)
            job.status = JobStatus.CANCELLED
            self._notify_callbacks("on_job_cancelled", job)
            return True

        logger.warning("Cannot cancel job %s: Job is in state %s", job_id, job.status)
        return False

    async def get_job_status(self, job_id: str) -> Optional[JobStatus]:
        """
        Get the current status of a job.

        Args:
            job_id: ID of the job to check

        Returns:
            The job's status, or None if the job doesn't exist
        """
        job = self.jobs.get(job_id)
        return job.status if job else None

    async def create_training_job(
        self,
        model_run_uuid: Union[str, UUID],
        training_data: Dict[str, Any],
        priority: int = JobPriority.NORMAL,
        profile: Optional[str] = None,
    ) -> str:
        """
        Create and schedule a new training job.

        Args:
            model_run_uuid: UUID of the model run
            training_data: Training data dictionary with model_components,
                          data_loaders, and training_config
            priority: Job priority
            profile: Database profile to use

        Returns:
            The job ID of the scheduled job
        """
        # Create job data
        job_data = TrainingJobData(
            model_run_uuid=model_run_uuid,
            profile=profile or self.config.profile,
            model_components=training_data.get("model_components", {}),
            data_loaders=training_data.get("data_loaders", {}),
            training_config=training_data.get("training_config", {}),
        )

        # Create job ID from model run UUID
        job_id = f"training-{model_run_uuid}"

        # Create and schedule the job
        job = TrainingJob(job_id=job_id, data=job_data, priority=priority)
        await self.schedule_job(job)

        return job_id

    async def _poll_for_jobs(self) -> None:
        """
        Poll the database for new training jobs.

        This method runs in a loop, checking for model runs with "scheduled" status
        and creating jobs for them.
        """
        try:
            while self._running:
                try:
                    # Get scheduled model runs from database
                    model_run_service = ModelRunService()
                    scheduled_runs = await maybe_await(
                        model_run_service.get_scheduled_runs(
                            profile=self.config.profile
                        )
                    )

                    # Create jobs for each scheduled run
                    for run in scheduled_runs:
                        # Skip if job already exists
                        job_id = f"training-{run.uuid}"
                        if job_id in self.jobs:
                            continue

                        # Get training data for the run
                        training_data = await maybe_await(
                            TrainingDataService.get_training_data(
                                run.uuid, profile=self.config.profile
                            )
                        )

                        # Create and schedule the job
                        await self.create_training_job(
                            model_run_uuid=run.uuid,
                            training_data=training_data,
                            priority=JobPriority.NORMAL,
                            profile=self.config.profile,
                        )

                    # Wait for next polling interval
                    await asyncio.sleep(self.config.polling_interval)

                except Exception as e:
                    logger.error("Error polling for jobs: %s", str(e))
                    await asyncio.sleep(self.config.polling_interval)

        except asyncio.CancelledError:
            logger.info("Job polling task cancelled")

    async def _process_queue(self) -> None:
        """
        Process jobs from the queue.

        This method runs in a loop, taking jobs from the queue and executing them
        according to resource constraints and priority.
        """
        try:
            while self._running:
                # Check if we can run more jobs
                if len(self.running_jobs) >= self.config.max_concurrent_jobs:
                    # Wait for a job to complete
                    await asyncio.sleep(1)
                    continue

                # Get next job from queue
                try:
                    job = await asyncio.wait_for(self.job_queue.get(), timeout=1)
                except asyncio.TimeoutError:
                    continue

                # Check if job is still valid
                if job.status == JobStatus.CANCELLED:
                    logger.info("Skipping cancelled job %s", job.job_id)
                    self.job_queue.task_done()
                    continue

                # Check if we have enough resources
                if not self._check_resources(job):
                    logger.info(
                        "Not enough resources for job %s, re-queuing", job.job_id
                    )
                    await self.job_queue.put(job)
                    self.job_queue.task_done()
                    await asyncio.sleep(5)  # Wait before checking again
                    continue

                # Execute the job
                logger.info("Starting job %s", job.job_id)
                task = asyncio.create_task(self._execute_job(job))
                self.running_jobs[job.job_id] = task
                self.job_queue.task_done()

        except asyncio.CancelledError:
            logger.info("Job processing task cancelled")

    def _check_resources(self, job: Job) -> bool:
        """
        Check if there are enough resources to run a job.

        Args:
            job: The job to check resources for

        Returns:
            True if there are enough resources, False otherwise
        """
        try:
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            if cpu_percent > self.config.max_cpu_usage_percent:
                logger.warning(
                    "CPU usage too high: %s%% > %s%%",
                    cpu_percent,
                    self.config.max_cpu_usage_percent,
                )
                return False

            # Check memory usage
            memory = psutil.virtual_memory()
            if memory.percent > self.config.max_memory_usage_percent:
                logger.warning("Memory usage too high: %s%%", memory.percent)
                return False

            # Check GPU if required
            if job.resources.gpu_required and torch.cuda.is_available():
                # Simple check for GPU memory
                # In a production system, you'd want a more sophisticated check
                gpu_memory_allocated = torch.cuda.memory_allocated() / (1024**3)
                gpu_memory_reserved = torch.cuda.memory_reserved() / (1024**3)

                # If we can't determine total memory, assume we have enough
                if gpu_memory_reserved > 0:
                    gpu_memory_percent = (
                        gpu_memory_allocated / gpu_memory_reserved
                    ) * 100
                    if gpu_memory_percent > self.config.max_gpu_memory_usage_percent:
                        logger.warning(
                            "GPU memory usage too high: %s%%", gpu_memory_percent
                        )
                        return False

            return True

        except Exception as e:
            logger.error("Error checking resources: %s", str(e))
            return True  # Default to allowing the job if we can't check resources

    async def _execute_job(self, job: Job) -> None:
        """
        Execute a job.

        Args:
            job: The job to execute
        """
        if not isinstance(job, TrainingJob):
            logger.error("Cannot execute job %s: Not a TrainingJob", job.job_id)
            return

        try:
            # Mark job as running
            job.status = JobStatus.RUNNING
            job.started_at = asyncio.get_event_loop().time()

            # Notify callbacks
            self._notify_callbacks("on_job_start", job)

            # Execute the training job
            result = await self._run_training(job)

            # Mark job as completed
            job.status = JobStatus.COMPLETED
            job.completed_at = asyncio.get_event_loop().time()
            job.result = result

            # Move job to completed jobs
            self.completed_jobs[job.job_id] = job

            # Notify callbacks
            self._notify_callbacks("on_job_complete", job)

            logger.info("Job %s completed successfully", job.job_id)

        except asyncio.CancelledError:
            logger.info("Job %s was cancelled", job.job_id)
            job.status = JobStatus.CANCELLED
            job.completed_at = asyncio.get_event_loop().time()
            self._notify_callbacks("on_job_cancelled", job)

        except Exception as e:
            logger.error("Job %s failed: %s", job.job_id, str(e))
            job.status = JobStatus.FAILED
            job.completed_at = asyncio.get_event_loop().time()
            job.error = e
            self._notify_callbacks("on_job_failed", job, e)

        finally:
            # Remove job from running jobs
            self.running_jobs.pop(job.job_id, None)

    async def _run_training(self, job: TrainingJob) -> ModelTrainer:
        """
        Run a training job using ModelTrainer.

        Args:
            job: The training job to run

        Returns:
            The ModelTrainer instance after training
        """
        # Get the full training data to access augmentations
        training_data = await maybe_await(
            TrainingDataService.get_training_data(
                job.data.model_run_uuid, profile=job.data.profile
            )
        )

        # Prepare data loaders with augmentation support
        data_loaders = await self._prepare_augmented_data_loaders(training_data, job)

        # Extract architecture_params from training_data for model validation
        architecture_params = {
            "name": training_data.get("model", {}).get("architecture", "Unknown"),
            "parameters": {
                "model_version": training_data.get("model_version", {}),
                "model_run": training_data.get("model_run", {}),
            },
        }

        # Create ModelRunCallback if not already in callbacks
        has_model_run_callback = any(
            isinstance(cb, ModelRunCallback) for cb in job.data.callbacks
        )

        if not has_model_run_callback:
            model_run_callback = ModelRunCallback(
                model_run_uuid=job.data.model_run_uuid, profile=job.data.profile
            )
            job.data.callbacks.append(model_run_callback)

        # Create trainer config with augmented data loaders
        trainer_config = TrainerConfig(
            model_components=job.data.model_components,
            data_loaders=data_loaders,
            training_config=job.data.training_config,
            callbacks=job.data.callbacks,
            database_config=DatabaseConfig(
                model_run_uuid=job.data.model_run_uuid, profile=job.data.profile
            ),
            architecture_params=architecture_params,
        )

        # Create and run trainer
        trainer = ModelTrainer(trainer_config)

        # Run training in a separate thread to avoid blocking the event loop
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, trainer.train)

        return trainer

    def _extract_augmentation_data(self, training_data: Dict[str, Any]) -> list:
        """Extract augmentation data from training data."""
        model_run_data = training_data.get("model_run", {})
        return model_run_data.get("augmentations", [])

    async def _ensure_dataset_images_available(
        self, dataset_uuid: str, dataset_data: Dict[str, Any], model_run_uuid: str
    ) -> None:
        """
        Ensure dataset images are available locally before training.

        Args:
            dataset_uuid: UUID of the dataset
            dataset_data: Dataset information from training data
            model_run_uuid: UUID of the model run
        """
        content_updated_at = dataset_data.get("content_updated_at")
        images_dir = get_dataset_images_dir(dataset_uuid, content_updated_at)

        update_data = ModelRunTimingUpdate(
            model_run_uuid=model_run_uuid,
            prepared_time=datetime.now(timezone.utc),
            profile=self.config.profile,
        )

        # Check cache first
        if DatasetStateCacheService.is_cache_valid(dataset_uuid, content_updated_at):
            logger.info(
                "Dataset %s is already prepared and cache is valid.", dataset_uuid
            )
            await ModelRunService.update_model_run_times(update_data)
            return

        logger.info("Dataset images not found locally or cache invalid, downloading...")

        try:
            # Use the existing download functionality
            downloaded_count, error_messages = await asyncio.to_thread(
                DatasetService.download_dataset_images,
                dataset_uuid=dataset_uuid,
                base_output_dir=images_dir,
                set_type=None,  # Download all set types
                profile=self.config.profile,
            )

            if error_messages:
                logger.warning(
                    "Download errors for dataset %s: %s", dataset_uuid, error_messages
                )

            if downloaded_count > 0:
                logger.info(
                    "Successfully downloaded %d images for dataset %s",
                    downloaded_count,
                    dataset_uuid,
                )
                # Update cache and prepared time
                DatasetStateCacheService.set_cache_state(
                    dataset_uuid, content_updated_at, is_prepared=True
                )
                await ModelRunService.update_model_run_times(update_data)
            else:
                logger.warning("No images were downloaded for dataset %s", dataset_uuid)

        except Exception as e:
            logger.error(
                "Failed to download images for dataset %s: %s", dataset_uuid, str(e)
            )
            # Don't raise here - let the data loading service handle the fallback

    def _create_data_loader_config(
        self, training_data: Dict[str, Any], job: TrainingJob
    ) -> DataLoaderConfig:
        """Create DataLoaderConfig from training data."""
        training_config = training_data.get("training_parameters", {})
        batch_size = training_config.get("batch_size", 32)

        return DataLoaderConfig(
            model_run_uuid=str(job.data.model_run_uuid),
            processing_config=DataProcessingConfig(
                batch_size=batch_size,
                use_mock_data=False,
            ),
            split_config=DataSplitConfig(test_size=0.2),
        )

    def _create_augmentation_metadata(
        self, augmentations: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Create augmentation metadata for artifact saving."""
        train_transforms = AugmentationPipelineFactory.create_training_pipeline(
            augmentations
        )
        val_transforms = AugmentationPipelineFactory.create_inference_pipeline(
            augmentations
        )

        return {
            "augmentations": augmentations,
            "train_transforms": train_transforms,
            "val_transforms": val_transforms,
            "augmentation_count": len(augmentations),
        }

    async def _prepare_augmented_data_loaders(
        self, training_data: Dict[str, Any], job: TrainingJob
    ) -> Dict[str, Any]:
        """
        Prepare data loaders with augmentation support.

        Args:
            training_data: Complete training data including model_run with augmentations
            job: The training job

        Returns:
            Dictionary containing augmented data loaders
        """
        try:
            # Extract and parse augmentations
            augmentations_data = self._extract_augmentation_data(training_data)
            augmentations = self._parse_augmentations(augmentations_data)

            logger.info(
                "Preparing data loaders with %d augmentations for job %s",
                len(augmentations),
                job.job_id,
            )

            # Get dataset information
            dataset_data = training_data.get("dataset", {})
            dataset_uuid = dataset_data.get("uuid")

            if not dataset_uuid:
                logger.error("No dataset UUID found in training data")
                raise ValueError(
                    "Dataset UUID is required for training but was not found in training data"
                )

            # Ensure dataset images are downloaded before creating data loaders
            await self._ensure_dataset_images_available(
                dataset_uuid, dataset_data, job.data.model_run_uuid
            )

            # Create data loaders with augmentations
            config = self._create_data_loader_config(training_data, job)
            data_loaders = await DataLoadingService.create_data_loaders(
                dataset_uuid=dataset_uuid,
                augmentations=augmentations,
                config=config,
            )

            # Add augmentation metadata to data loaders result
            data_loaders["_augmentation_metadata"] = self._create_augmentation_metadata(
                augmentations
            )

            logger.info(
                "Created augmented data loaders: Train=%d samples, Test=%d samples",
                len(data_loaders["train"].dataset),
                len(data_loaders["test"].dataset),
            )

            return data_loaders

        except Exception as e:
            logger.error(
                "Error preparing augmented data loaders for job %s: %s",
                job.job_id,
                str(e),
            )
            # Fall back to original data loaders if augmentation preparation fails
            return job.data.data_loaders

    def _parse_augmentations(self, augmentations_data):
        """Parse augmentation data from dictionaries to objects."""
        augmentations = []
        if augmentations_data:
            try:
                augmentations = AugmentationUtils.from_dict_list(augmentations_data)
                logger.info(
                    "Successfully parsed %d augmentations: %s",
                    len(augmentations),
                    [type(aug).__name__ for aug in augmentations],
                )
            except Exception as e:
                logger.warning(
                    "Failed to parse augmentations, using empty list: %s", str(e)
                )
                augmentations = []
        return augmentations
