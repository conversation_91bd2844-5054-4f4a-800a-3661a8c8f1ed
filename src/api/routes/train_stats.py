"""
API route for train / model_run stats.
"""

import json
from typing import Any, Dict

from fastapi import APIRouter, HTTPException, Request, status
from pydantic import BaseModel, ConfigDict, Field

from api.utils.error_handlers import INTERNAL_SERVER_ERROR_RESPONSE, api_route_handler
from src.config.paths import (
    get_logs_dir,
    get_metrics_history_path,
    get_metrics_summary_path,
)

router = APIRouter(
    prefix="",
    tags=["train"],
    responses=INTERNAL_SERVER_ERROR_RESPONSE,
)


class ModelRunStatsResponse(BaseModel):
    """Summary metrics for the model run."""

    summary: Dict[str, Any] = Field(
        ..., description="Summary metrics for the model run"
    )
    history: Dict[str, Any] = Field(
        ..., description="Detailed metrics history for the model run"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "summary": {
                    "final": {"train_loss": 0.69, "train_accuracy": 0.49},
                    "timing": {"total_training_time": 16.4},
                    "resources": {"cpu_percent_max": 37.1},
                },
                "history": {
                    "batch_metrics": [
                        {
                            "epoch": 0,
                            "batch": 0,
                            "loss": 2.67,
                            "accuracy": 0.43,
                            "time": 0.65,
                        },
                        # ...
                    ]
                },
            }
        }
    )


class ModelRunLogsResponse(BaseModel):
    """Log files for the model run, as a mapping of filename to content."""

    logs: Dict[str, str] = Field(
        ...,
        description="Log files for the model run, as a mapping of filename to content",
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "logs": {
                    "trained_model_script_training.log": "2025-06-23 16:38:52 - ..."
                }
            }
        }
    )


@router.get(
    "/train/{model_run_uuid}/stats",
    response_model=ModelRunStatsResponse,
    summary="Get Model Run Stats",
    description="Retrieves detailed stats and metrics for a specific model run.",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "description": "Model run stats not found (possibly cleaned)",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Stats for model_run_uuid not found or cleaned."
                    }
                }
            },
        }
    },
)
@api_route_handler("retrieving model run stats")
async def get_model_run_stats(
    _request: Request, model_run_uuid: str
) -> ModelRunStatsResponse:
    """Get detailed stats for a specific model run."""
    summary_path = get_metrics_summary_path(model_run_uuid)
    history_path = get_metrics_history_path(model_run_uuid)

    if not (summary_path.exists() and history_path.exists()):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Stats for {model_run_uuid} not found or cleaned.",
        )

    with open(summary_path, "r", encoding="utf-8") as f:
        summary = json.load(f)
    with open(history_path, "r", encoding="utf-8") as f:
        history = json.load(f)

    return ModelRunStatsResponse(summary=summary, history=history)


@router.get(
    "/train/{model_run_uuid}/logs",
    response_model=ModelRunLogsResponse,
    summary="Get Model Run Logs",
    description="Retrieves all log files for a specific model run.",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "description": "Model run logs not found (possibly cleaned)",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Logs for model_run_uuid not found or cleaned."
                    }
                }
            },
        }
    },
)
@api_route_handler("retrieving model run logs")
async def get_model_run_logs(
    _request: Request, model_run_uuid: str
) -> ModelRunLogsResponse:
    """Get all logs for a specific model run."""
    logs_dir = get_logs_dir(model_run_uuid)

    if not logs_dir.is_dir():
        # If logs directory is missing, return empty logs dict (not 404)
        return ModelRunLogsResponse(logs={})

    logs = {}
    for fpath in logs_dir.iterdir():
        if fpath.is_file():
            try:
                with open(fpath, "r", encoding="utf-8") as lf:
                    logs[fpath.name] = lf.read()
            except Exception:
                logs[fpath.name] = "[Could not read log file]"

    return ModelRunLogsResponse(logs=logs)
