"""
Artifact management for training runs.

This module handles saving and managing various artifacts produced during training,
including models, metrics, augmentation configurations, and label mappings.
"""

import json
import logging
import traceback
from typing import Any, Dict, Optional

from datasets.dataset_utils import extract_augmentation_metadata_from_data_loaders
from datasets.label_artifacts import LabelArtifactManager
from src.augmentations import AugmentationSerializer
from src.config.paths import get_augmentations_dir, get_run_dir
from src.models.persistence import ModelData, ModelPersistence

logger = logging.getLogger(__name__)


class TrainingArtifactManager:
    """
    Manages the saving and organization of training artifacts.

    This class handles the persistence of various artifacts generated during
    training, ensuring they are properly organized and accessible for inference.
    """

    def __init__(self, config, components, data_loaders, metrics_persistence):
        """
        Initialize the artifact manager.

        Args:
            config: Training configuration
            components: Model components (model, optimizer, etc.)
            data_loaders: Data loaders used in training
            metrics_persistence: Metrics persistence handler
        """
        self.config = config
        self.components = components
        self.data_loaders = data_loaders
        self.metrics_persistence = metrics_persistence
        self.metrics = None  # Will be set by the trainer before saving
        self.logger = logger
        self.model_persistence = ModelPersistence()

    def save_all_artifacts(self):
        """
        Save all training artifacts.

        This includes model checkpoints, metrics, augmentation configurations,
        and label mappings.
        """
        self.logger.info("Saving artifacts...")
        try:
            # Save model checkpoint
            self._save_model_checkpoint()

            # Save metrics
            self._save_metrics()

            # Save augmentation artifacts if available
            self._save_augmentation_artifacts()

            # Save label artifacts
            self._save_label_artifacts()

        except Exception as e:
            self.logger.error("Error saving artifacts: %s", str(e))
            self.logger.error(traceback.format_exc())

    def save_model_only(self):
        """
        Save only the model artifacts.

        Useful for checkpointing during training without saving all artifacts.
        """
        self.logger.info("Saving model artifacts only...")
        self._save_model_checkpoint()

    def save_metrics_only(self):
        """
        Save only the metrics artifacts.

        Useful for periodic metrics saving during training.
        """
        self.logger.info("Saving metrics artifacts only...")
        self._save_metrics()

    def get_artifact_status(self) -> Dict[str, bool]:
        """
        Get the status of different artifact types.

        Returns:
            Dictionary indicating which artifact types are available for saving
        """
        status = {
            "model": self.components.model is not None,
            "metrics": hasattr(self, "metrics") and self.metrics is not None,
            "augmentations": bool(self._extract_augmentation_metadata()),
            "labels": bool(self.data_loaders),
        }
        return status

    def _save_model_checkpoint(self):
        """Save the trained model checkpoint using ModelPersistence."""
        # Prepare metadata with config, scheduler state, and metrics
        metadata = {
            "config": self.config,
        }

        # Add scheduler state if available
        if hasattr(self.components, "scheduler") and self.components.scheduler:
            metadata["scheduler_state_dict"] = self.components.scheduler.state_dict()

        # Add metrics if available
        if hasattr(self, "metrics") and self.metrics:
            metadata["metrics"] = self.metrics

        # Create ModelData with model, optimizer, and metadata
        model_data = ModelData(
            model=self.components.model,
            optimizer=getattr(self.components, "optimizer", None),
            metadata=metadata,
        )

        # Save using ModelPersistence
        saved_path = self.model_persistence.save_model(
            model_data, self.config.model_run_uuid
        )
        self.logger.info("Model saved to %s", saved_path)

    def _save_metrics(self):
        """Save training metrics to files."""
        if not self.metrics_persistence:
            self.logger.warning(
                "No metrics persistence available, skipping metrics save"
            )
            return

        if not hasattr(self, "metrics") or not self.metrics:
            self.logger.warning("No metrics available, skipping metrics save")
            return

        self.logger.info("Saving metrics files...")

        output_dir = get_run_dir(self.config.model_run_uuid)

        # Use MetricsPersistence to save both detailed history and summary
        saved_files = self.metrics_persistence.save_metrics(
            metrics=self.metrics, output_dir=output_dir
        )

        if "history" in saved_files:
            self.logger.info(
                "Saved detailed metrics history to %s", saved_files["history"]
            )
        if "summary" in saved_files:
            self.logger.info("Saved metrics summary to %s", saved_files["summary"])

    def _save_augmentation_artifacts(self):
        """
        Save augmentation pipelines and configurations for inference compatibility.
        """
        try:
            # Check if augmentation metadata is available in data loaders
            augmentation_metadata = self._extract_augmentation_metadata()

            if not augmentation_metadata:
                self.logger.info(
                    "No augmentation metadata found, skipping augmentation artifacts"
                )
                return

            self.logger.info("Saving augmentation artifacts...")

            # Get augmentations directory for this model run
            augmentations_dir = get_augmentations_dir(self.config.model_run_uuid)
            augmentations_dir.mkdir(parents=True, exist_ok=True)

            # Save augmentation configurations
            saved_files = {}
            for split_name, metadata in augmentation_metadata.items():
                if metadata and "augmentation_config" in metadata:
                    config_path = (
                        augmentations_dir / f"{split_name}_augmentation_config.json"
                    )

                    # Use AugmentationSerializer to save the configuration
                    AugmentationSerializer.save_augmentation_config(
                        metadata["augmentation_config"], config_path
                    )
                    saved_files[f"{split_name}_config"] = str(config_path)

                    self.logger.info(
                        "Saved %s augmentation config to %s", split_name, config_path
                    )

            # Save a summary of all augmentation artifacts
            if saved_files:
                summary_path = augmentations_dir / "augmentation_summary.json"

                with summary_path.open("w") as f:
                    json.dump(
                        {
                            "model_run_uuid": str(self.config.model_run_uuid),
                            "saved_files": saved_files,
                            "augmentation_metadata": augmentation_metadata,
                        },
                        f,
                        indent=2,
                        default=str,
                    )

                self.logger.info("Saved augmentation summary to %s", summary_path)

        except Exception as e:
            self.logger.warning("Failed to save augmentation artifacts: %s", str(e))
            # Don't raise the exception as this is not critical for training

    def _save_label_artifacts(self):
        """
        Save label artifacts including label mappings and class information.
        """
        try:
            if not self.data_loaders:
                self.logger.info("No data loaders available, skipping label artifacts")
                return

            self.logger.info("Saving label artifacts...")

            # Save label artifacts with additional training metadata
            additional_metadata = {
                "training_completed": True,
                "final_epoch": (
                    getattr(self.metrics, "epoch", 0) if hasattr(self, "metrics") else 0
                ),
                "model_architecture": getattr(self.config, "architecture_params", {}),
            }

            saved_files = LabelArtifactManager.save_label_artifacts(
                data_loaders=self.data_loaders,
                model_run_uuid=self.config.model_run_uuid,
                additional_metadata=additional_metadata,
            )

            self.logger.info(
                "Successfully saved label artifacts: %s",
                list(saved_files.keys()),
            )

        except Exception as e:
            self.logger.warning("Failed to save label artifacts: %s", str(e))
            # Don't raise the exception as this is not critical for training

    def _extract_augmentation_metadata(self) -> Dict[str, Optional[Dict[str, Any]]]:
        """
        Extract augmentation metadata from data loaders.

        Uses the centralized utility function for consistent metadata extraction
        across the codebase.

        Returns:
            Dictionary mapping split names to their augmentation metadata
        """
        try:
            metadata = extract_augmentation_metadata_from_data_loaders(
                self.data_loaders
            )

            # Log debug information about what was found
            for split_name, split_metadata in metadata.items():
                if split_metadata is not None:
                    self.logger.debug(
                        "Found augmentation metadata for %s split", split_name
                    )
                else:
                    self.logger.debug(
                        "No augmentation metadata found for %s split", split_name
                    )

            return metadata

        except Exception as e:
            self.logger.warning("Error extracting augmentation metadata: %s", str(e))
            # Return empty metadata for all splits if extraction fails
            return {
                split_name: None
                for split_name in self.data_loaders.keys()
                if not split_name.startswith("_")
            }
