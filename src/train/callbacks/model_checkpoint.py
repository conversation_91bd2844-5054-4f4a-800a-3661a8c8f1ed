# src/train/callbacks/model_checkpoint.py
"""
Model checkpoint callback for saving model weights during training.
"""

import logging
from typing import Any, Dict

import torch

from src.config.paths import get_checkpoint_path

from .base import Callback

logger = logging.getLogger(__name__)


class ModelCheckpoint(Callback):
    """
    Callback to save the model checkpoint.

    This callback saves the model's weights at the end of every epoch
    if the monitored metric has improved.
    """

    def __init__(  # pylint: disable=too-many-arguments
        self,
        model_run_uuid: str,
        monitor: str = "validation_loss",
        mode: str = "min",
        *,
        save_best_only: bool = True,
        verbose: bool = True,
    ):
        super().__init__()
        self.model_run_uuid = model_run_uuid
        self.monitor = monitor
        self.mode = mode
        self.save_best_only = save_best_only
        self.verbose = verbose
        self.best_score = float("inf") if self.mode == "min" else float("-inf")

        if self.mode not in ["min", "max"]:
            raise ValueError(
                f"ModelCheckpoint mode '{self.mode}' is unknown, use 'min' or 'max'."
            )

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of an epoch to save model if metrics improved."""
        logs = logs or {}
        current_score = logs.get(self.monitor)

        if current_score is None:
            logger.warning(
                "ModelCheckpoint conditioned on unavailable metric `%s`. Available metrics are: %s",
                self.monitor,
                ",".join(logs.keys()),
            )
            return

        if self.mode == "min":
            improved = current_score < self.best_score
        else:  # mode == "max"
            improved = current_score > self.best_score

        if improved:
            previous_best = self.best_score
            self.best_score = current_score

            if self.trainer and self.trainer.components.model:
                # Determine the filepath to use
                filepath = get_checkpoint_path(self.model_run_uuid)

                # Ensure the directory exists
                filepath.parent.mkdir(parents=True, exist_ok=True)

                if self.verbose:
                    logger.info(
                        "Epoch %d: %s improved from %.4f to %.4f, saving model to %s",
                        epoch,
                        self.monitor,
                        previous_best,
                        self.best_score,
                        filepath,
                    )
                torch.save(self.trainer.components.model.state_dict(), filepath)
