# src/train/callbacks/feature_map_visualization.py
"""
Feature map visualization callback for the ModelTrainer.

This callback generates and saves feature map visualizations during training
for models that support feature extraction (CNN models with Conv2d layers).
"""

import logging
import os
from typing import Any, Dict, Optional

from src.config.paths import get_plots_dir
from src.utils.feature_extraction import can_extract_feature_maps, extract_feature_maps
from src.utils.plots import plot_correlation_values, plot_feature_maps

from .base import Callback

logger = logging.getLogger(__name__)


class FeatureMapVisualizationCallback(Callback):
    """
    Callback to generate and save feature map visualizations during training.

    This callback automatically detects if the model supports feature extraction
    and generates visualizations at the end of training. It creates both feature
    map plots and correlation analysis plots.
    """

    def __init__(
        self,
        model_run_uuid: str,
        num_samples: int = 10,
        layer_name: Optional[str] = None,
        enable_correlation_plots: bool = True,
    ):
        """
        Initialize the feature map visualization callback.

        Args:
            model_run_uuid: UUID of the model run for path generation
            num_samples: Number of samples to visualize (default: 10)
            layer_name: Specific layer name to extract from. If None, uses first Conv2d layer
            enable_correlation_plots: Whether to generate correlation plots (default: True)
        """
        super().__init__()
        self.model_run_uuid = model_run_uuid
        self.num_samples = num_samples
        self.layer_name = layer_name
        self.enable_correlation_plots = enable_correlation_plots
        self.plots_dir = get_plots_dir(model_run_uuid)
        os.makedirs(self.plots_dir, exist_ok=True)

    def on_train_end(
        self, logs: Optional[Dict[str, Any]] = None
    ) -> None:  # pylint: disable=unused-argument
        """Generate feature map plots at the end of training with robust error handling."""
        # Quick exit if trainer is not available
        if not self.trainer:
            logger.debug(
                "FeatureMapVisualizationCallback: No trainer available, skipping"
            )
            return

        try:
            self._safe_generate_visualizations()
        except Exception as e:
            logger.error(
                "Unexpected error in feature map visualization: %s",
                str(e),
                exc_info=True,
            )

    def _safe_generate_visualizations(self) -> None:
        """Safely generate visualizations with comprehensive error handling."""
        # Validate and prepare for visualization
        processed_data = self._prepare_visualization_data()
        if processed_data is None:
            return

        feature_maps, x_sample, y_sample = processed_data

        # Generate plots with individual error handling
        self._create_feature_map_plot_safely(feature_maps, x_sample, y_sample)

        # Generate correlation plot if enabled and we have enough feature maps
        if self.enable_correlation_plots and feature_maps.shape[1] >= 2:
            self._create_correlation_plot_safely(feature_maps)
        elif self.enable_correlation_plots:
            logger.info("Skipping correlation plot: need at least 2 feature maps")

        logger.info("Feature map visualization completed successfully")

    def _prepare_visualization_data(self) -> Optional[tuple]:
        """Prepare all data needed for visualization. Returns None if any step fails."""
        # Validate trainer and model
        model = self._validate_and_get_model()
        if model is None:
            return None

        # Get and process test data
        return self._extract_and_process_data(model)

    def _validate_and_get_model(self) -> Optional[object]:
        """Validate trainer and get model if feature extraction is supported."""
        # Basic validation
        if not self._validate_trainer():
            return None

        logger.info("Generating feature map visualizations...")

        # Get model and device with error handling
        try:
            model = self.trainer.components.model
            device = self.trainer.device
            logger.debug("Using device: %s for feature map extraction", device)
        except Exception as e:
            logger.error("Error accessing trainer components: %s", str(e))
            return None

        # Check if model supports feature extraction
        try:
            if not can_extract_feature_maps(model):
                logger.info(
                    "Model does not support feature map extraction, skipping visualization"
                )
                return None
        except Exception as e:
            logger.error("Error checking feature extraction support: %s", str(e))
            return None

        return model

    def _extract_and_process_data(self, model) -> Optional[tuple]:
        """Extract feature maps and process data for visualization."""
        # Get test data with robust error handling
        test_data = self._get_test_data_safely()
        if test_data is None:
            return None

        x_sample, y_sample = test_data

        # Extract feature maps
        feature_maps = self._extract_feature_maps_safely(model, x_sample)
        if feature_maps is None:
            return None

        logger.info("Extracted feature maps with shape: %s", feature_maps.shape)

        # Move data back to CPU for plotting
        try:
            feature_maps = feature_maps.cpu()
            x_sample = x_sample.cpu()
            y_sample = y_sample.cpu()
        except Exception as e:
            logger.error("Error moving tensors to CPU: %s", str(e))
            return None

        return feature_maps, x_sample, y_sample

    def _create_feature_map_plot(self, feature_maps, images, labels):
        """Create and save the main feature map visualization plot."""
        output_path = os.path.join(self.plots_dir, "feature_maps.png")
        logger.debug("Saving feature map plot to: %s", output_path)

        try:
            plot_feature_maps(
                feature_maps=feature_maps,
                images=images,
                true_labels=labels,
                output_path=output_path,
            )
            logger.debug("Feature map plot saved successfully")
        except Exception as e:
            logger.error("Error creating feature map plot: %s", str(e))

    def _create_correlation_plot(self, feature_maps):
        """Create and save the feature map correlation plot."""
        output_path = os.path.join(self.plots_dir, "feature_map_correlations.png")
        logger.debug("Saving correlation plot to: %s", output_path)

        try:
            plot_correlation_values(
                feature_maps=feature_maps,
                output_path=output_path,
            )
            logger.debug("Correlation plot saved successfully")
        except Exception as e:
            logger.error("Error creating correlation plot: %s", str(e))

    def _validate_trainer(self) -> bool:
        """Validate that trainer is available and properly configured."""
        if not self.trainer:
            logger.warning("FeatureMapVisualizationCallback: No trainer available")
            return False

        if not hasattr(self.trainer, "components") or not self.trainer.components:
            logger.warning(
                "FeatureMapVisualizationCallback: Trainer components not available"
            )
            return False

        if (
            not hasattr(self.trainer.components, "model")
            or not self.trainer.components.model
        ):
            logger.warning("FeatureMapVisualizationCallback: Model not available")
            return False

        return True

    def _get_test_data_safely(self) -> Optional[tuple]:
        """Safely get test data with comprehensive error handling."""
        # Check if test loader is available
        if (
            not hasattr(self.trainer, "data_loaders")
            or not self.trainer.data_loaders
            or "test" not in self.trainer.data_loaders
        ):
            logger.warning("FeatureMapVisualizationCallback: No test loader available")
            return None

        test_loader = self.trainer.data_loaders["test"]

        if not test_loader:
            logger.warning("FeatureMapVisualizationCallback: Test loader is empty")
            return None

        logger.debug("Found test loader with %d batches", len(test_loader))

        # Get a batch from test loader
        try:
            x_batch, y_batch = next(iter(test_loader))

            # Move to device safely
            device = self.trainer.device
            x_batch = x_batch.to(device)
            y_batch = y_batch.to(device)

            logger.debug("Got batch with shape: %s", x_batch.shape)

            # Limit to specified number of samples for visualization
            num_samples = min(self.num_samples, len(x_batch))
            x_sample = x_batch[:num_samples]
            y_sample = y_batch[:num_samples]

            return x_sample, y_sample
        except StopIteration:
            logger.warning("Test loader is empty (no batches)")
            return None
        except Exception as e:
            logger.error("Error getting batch from test loader: %s", str(e))
            return None

    def _extract_feature_maps_safely(self, model, inputs) -> Optional[object]:
        """Safely extract feature maps with timeout and error handling."""
        try:
            # Set model to evaluation mode
            model.eval()

            # Extract feature maps
            feature_maps = extract_feature_maps(model, inputs, self.layer_name)

            if feature_maps is None:
                logger.warning("Failed to extract feature maps from model")
                return None

            return feature_maps
        except Exception as e:
            logger.error("Error during feature map extraction: %s", str(e))
            return None

    def _create_feature_map_plot_safely(self, feature_maps, images, labels):
        """Create and save the main feature map visualization plot with error handling."""
        try:
            self._create_feature_map_plot(feature_maps, images, labels)
        except Exception as e:
            logger.error("Error creating feature map plot safely: %s", str(e))

    def _create_correlation_plot_safely(self, feature_maps):
        """Create and save the feature map correlation plot with error handling."""
        try:
            self._create_correlation_plot(feature_maps)
        except Exception as e:
            logger.error("Error creating correlation plot safely: %s", str(e))
