"""Logging configuration for the application."""

import logging
from pathlib import Path
from typing import Optional, Union

from config.paths import (
    get_dataset_logs_dir,
    get_logs_dir,
    get_runs_base_dir,
    set_runs_base_dir,
)


def setup_loggers(model_run_uuid: str):
    """
    Set up loggers for different components of the application.

    Args:
        model_run_uuid: Model run UUID for creating logs in the run-specific directory.
    """
    # Use model run UUID to determine logs directory
    logs_dir = get_logs_dir(model_run_uuid)

    # Create logs directory if it doesn't exist
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # Configure console handler for root logger
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)

    # Configure dataset logger
    dataset_logger = logging.getLogger("dataset")
    dataset_logger.setLevel(logging.INFO)
    dataset_logger.propagate = False  # Don't propagate to root logger

    # File handler for dataset logger
    dataset_file_handler = logging.FileHandler(logs_dir / "dataset.log")
    dataset_file_handler.setLevel(logging.INFO)
    dataset_formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    dataset_file_handler.setFormatter(dataset_formatter)
    dataset_logger.addHandler(dataset_file_handler)

    # Console handler for dataset logger
    dataset_console_handler = logging.StreamHandler()
    dataset_console_handler.setLevel(logging.INFO)
    dataset_console_handler.setFormatter(dataset_formatter)
    dataset_logger.addHandler(dataset_console_handler)

    return {
        "root": root_logger,
        "dataset": dataset_logger,
    }


def setup_dataset_logger(
    model_run_uuid: str, *, runs_base_dir: Optional[Union[str, Path]] = None
):
    """
    Set up a dataset logger for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.
        runs_base_dir: Optional custom base directory for runs. If provided,
                      temporarily overrides the global runs configuration.

    Returns:
        The configured dataset logger.
    """
    if runs_base_dir:
        # Temporarily override the global runs config for testing
        original_base = get_runs_base_dir()
        set_runs_base_dir(runs_base_dir)
        try:
            loggers = setup_loggers(model_run_uuid)
            return loggers["dataset"]
        finally:
            # Restore original config
            set_runs_base_dir(original_base)
    else:
        # Use default production behavior
        loggers = setup_loggers(model_run_uuid)
        return loggers["dataset"]


def setup_dataset_logger_for_dataset(
    dataset_uuid: str,
    content_updated_at: Optional[str] = None,
    model_run_uuid: Optional[str] = None,
) -> logging.Logger:
    """
    Set up a dataset logger that can save to either model run directory or dataset directory.

    Args:
        dataset_uuid: The UUID of the dataset
        content_updated_at: Optional content update timestamp for versioning
        model_run_uuid: Optional model run UUID. If provided, logs to model run directory.
                       If None, logs to dataset directory.

    Returns:
        The configured dataset logger.
    """
    # Determine log directory based on whether model_run_uuid is provided
    if model_run_uuid:
        # Use model run directory (existing behavior)
        logs_dir = get_logs_dir(model_run_uuid)
        logger_name = f"dataset.{model_run_uuid}"
        log_filename = "dataset.log"
    else:
        # Use dataset directory (new behavior)
        logs_dir = get_dataset_logs_dir(dataset_uuid, content_updated_at)
        logger_name = f"dataset.{dataset_uuid}"
        log_filename = "dataset.log"

    # Create logs directory if it doesn't exist
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Configure dataset logger
    dataset_logger = logging.getLogger(logger_name)
    dataset_logger.setLevel(logging.INFO)
    dataset_logger.propagate = False  # Don't propagate to root logger

    # Clear existing handlers to avoid duplicates
    if dataset_logger.hasHandlers():
        dataset_logger.handlers.clear()

    # File handler for dataset logger
    log_file_path = logs_dir / log_filename
    dataset_file_handler = logging.FileHandler(log_file_path)
    dataset_file_handler.setLevel(logging.INFO)
    dataset_formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    dataset_file_handler.setFormatter(dataset_formatter)
    dataset_logger.addHandler(dataset_file_handler)

    # Console handler for dataset logger
    dataset_console_handler = logging.StreamHandler()
    dataset_console_handler.setLevel(logging.INFO)
    dataset_console_handler.setFormatter(dataset_formatter)
    dataset_logger.addHandler(dataset_console_handler)

    return dataset_logger
