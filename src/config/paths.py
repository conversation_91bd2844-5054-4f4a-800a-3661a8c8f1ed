"""
Configuration module for file and directory paths used throughout the application.

DIRECTORY STRUCTURE:
This module manages a centralized artifacts directory structure that organizes
all training-related data under a single parent directory for better organization
and easier management.

project-root/
├── artifacts/                           # Main artifacts directory (git-ignored)
│   ├── runs/                           # Model training runs
│   │   └── {model_run_uuid}/           # Individual training run
│   │       ├── logs/                   # Training logs
│   │       │   └── training.log
│   │       ├── metrics/                # Training metrics
│   │       │   ├── summary.json
│   │       │   └── history.json
│   │       ├── plots/                  # Training visualizations
│   │       │   ├── losses.png
│   │       │   └── accuracy.png
│   │       ├── checkpoints/            # Model checkpoints
│   │       │   ├── best_model.pt
│   │       │   └── checkpoint_epoch_*.pt
│   │       ├── augmentations/          # Augmentation configs
│   │       ├── labels/                 # Label mappings
│   │       └── model.pt                # Final trained model
│   └── datasets/                       # Dataset storage
│       └── {dataset_uuid}_{timestamp}/ # Versioned dataset
│           ├── images/                 # Dataset images
│           │   └── {coin_side_uuid}/   # Organized by coin side (labeled)
│           │       └── {image_uuid}.jpg
│           │   └── background/         # Images with no labels
│           │       └── {image_uuid}.jpg
│           └── logs/                   # Dataset processing logs
├── tests/manual/outcome/               # Manual test outputs (isolated)
│   └── {test_name}/                    # Test-specific outputs
│       ├── *.png                       # Generated visualizations
│       ├── *.json                      # Test results
│       └── *.log                       # Test logs
└── static/                             # Static image assets (production / external)
    ├── coins/                          # Coin images
    ├── facts/                          # Fact images
    └── scrapes/                        # Scraped images

ISOLATION:
- Production artifacts are stored under artifacts/
- Manual test outputs are isolated in tests/manual/outcome/
- Test runs use temporary directories to prevent production interference
- All paths are configurable through the centralized configuration classes
"""

from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Union

# Type alias for path-like objects
PathLike = Union[str, Path]

# Base directories
ARTIFACTS_DIR = Path("artifacts")
RUNS_BASE_DIR = ARTIFACTS_DIR / "runs"
DATASETS_BASE_DIR = ARTIFACTS_DIR / "datasets"
MANUAL_TEST_OUTPUT_DIR = Path("tests/manual/outcome")

# Static directories (external assets)
BACKGROUND_DIR = "background"
DIR_STATIC = "static/"
DIR_COINS = f"{DIR_STATIC}coins"
DIR_FACTS = f"{DIR_STATIC}facts"
DIR_FACTS_COINS = f"{DIR_FACTS}/coins"
DIR_FACTS_BANKNOTES = f"{DIR_FACTS}/banknotes"
DIR_FACTS_MISC = f"{DIR_FACTS}/miscellaneous"
DIR_FACTS_STAMPS = f"{DIR_FACTS}/stamps"
DIR_FACTS_WATCH = f"{DIR_FACTS}/watches"
DIR_SCRAPES = f"{DIR_STATIC}scrapes"


class ArtifactConfig:
    """Unified configuration for all artifact storage paths."""

    def __init__(self):
        self.runs_base = RUNS_BASE_DIR
        self.datasets_base = DATASETS_BASE_DIR
        self.manual_test_output = MANUAL_TEST_OUTPUT_DIR

    def set_runs_base(self, path: PathLike) -> None:
        """Set the base directory for runs storage."""
        self.runs_base = Path(path)

    def set_datasets_base(self, path: PathLike) -> None:
        """Set the base directory for datasets storage."""
        self.datasets_base = Path(path)


class RunPaths:
    """Path management for training runs."""

    def __init__(self, run_uuid: str, config: ArtifactConfig):
        self.run_uuid = run_uuid
        self.config = config
        self._base = config.runs_base / run_uuid

    @property
    def base(self) -> Path:
        """Base directory for this run."""
        return self._base

    @property
    def logs(self) -> Path:
        """Logs directory."""
        return self._base / "logs"

    @property
    def metrics(self) -> Path:
        """Metrics directory."""
        return self._base / "metrics"

    @property
    def plots(self) -> Path:
        """Plots directory."""
        return self._base / "plots"

    @property
    def checkpoints(self) -> Path:
        """Checkpoints directory."""
        return self._base / "checkpoints"

    @property
    def augmentations(self) -> Path:
        """Augmentations directory."""
        return self._base / "augmentations"

    @property
    def labels(self) -> Path:
        """Labels directory."""
        return self._base / "labels"

    @property
    def model_file(self) -> Path:
        """Path to the final model file."""
        return self._base / "model.pt"

    @property
    def training_log(self) -> Path:
        """Path to the training log file."""
        return self.logs / "training.log"

    @property
    def metrics_summary(self) -> Path:
        """Path to the metrics summary file."""
        return self.metrics / "summary.json"

    @property
    def metrics_history(self) -> Path:
        """Path to the metrics history file."""
        return self.metrics / "history.json"

    def checkpoint_file(self, epoch: Optional[int] = None) -> Path:
        """Path to a checkpoint file. If epoch is None, returns best model."""
        if epoch is None:
            return self.checkpoints / "best_model.pt"
        return self.checkpoints / f"checkpoint_epoch_{epoch}.pt"

    def plot_file(self, filename: str) -> Path:
        """Path to a plot file."""
        return self.plots / filename

    def ensure_directories(self) -> Dict[str, Path]:
        """Create all directories and return their paths."""
        directories = {
            "run": self.base,
            "logs": self.logs,
            "metrics": self.metrics,
            "plots": self.plots,
            "checkpoints": self.checkpoints,
            "augmentations": self.augmentations,
            "labels": self.labels,
        }

        for dir_path in directories.values():
            dir_path.mkdir(parents=True, exist_ok=True)

        return directories


class DatasetPaths:
    """Path management for datasets."""

    def __init__(self, dataset_uuid: str, config: ArtifactConfig, content_updated_at: Optional[str] = None):
        self.dataset_uuid = dataset_uuid
        self.config = config
        self.content_updated_at = content_updated_at
        self._base = self._build_base_path()

    def _build_base_path(self) -> Path:
        """Build the base path with optional timestamp."""
        if self.content_updated_at:
            try:
                # Parse timestamp and convert to Unix timestamp for filesystem safety
                dt = datetime.fromisoformat(self.content_updated_at.replace("Z", "+00:00"))
                unix_timestamp = int(dt.timestamp())
                dataset_name = f"{self.dataset_uuid}_{unix_timestamp}"
            except (ValueError, AttributeError):
                # Fallback to UUID only if timestamp parsing fails
                dataset_name = self.dataset_uuid
        else:
            dataset_name = self.dataset_uuid

        return self.config.datasets_base / dataset_name

    @property
    def base(self) -> Path:
        """Base directory for this dataset."""
        return self._base

    @property
    def images(self) -> Path:
        """Images directory."""
        return self._base / "images"

    @property
    def logs(self) -> Path:
        """Logs directory."""
        return self._base / "logs"

    def coin_side_dir(self, coin_side_uuid: str) -> Path:
        """Directory for a specific coin side."""
        return self.images / coin_side_uuid

    def image_path(self, coin_side_uuid: str, image_uuid: str, extension: str = "jpg") -> Path:
        """Path to a specific image file."""
        return self.coin_side_dir(coin_side_uuid) / f"{image_uuid}.{extension}"

    def ensure_directories(self) -> Dict[str, Path]:
        """Create all directories and return their paths."""
        directories = {
            "dataset": self.base,
            "images": self.images,
            "logs": self.logs,
        }

        for dir_path in directories.values():
            dir_path.mkdir(parents=True, exist_ok=True)

        return directories


# Global configuration instance
_artifact_config = ArtifactConfig()


# Factory functions for path managers
def get_run_paths(model_run_uuid: str) -> RunPaths:
    """Get path manager for a training run."""
    return RunPaths(model_run_uuid, _artifact_config)


def get_dataset_paths(dataset_uuid: str, content_updated_at: Optional[str] = None) -> DatasetPaths:
    """Get path manager for a dataset."""
    return DatasetPaths(dataset_uuid, _artifact_config, content_updated_at)


def get_manual_test_output_dir(test_name: Optional[str] = None) -> Path:
    """Get the output directory path for manual tests."""
    if test_name:
        return _artifact_config.manual_test_output / test_name
    return _artifact_config.manual_test_output


def get_background_dir_name() -> str:
    """Get the background directory name for unlabeled images."""
    return BACKGROUND_DIR


def get_plot_path(
    filename: str,
    output_path: Optional[PathLike] = None,
    model_run_uuid: Optional[str] = None,
) -> Optional[Path]:
    """
    Helper function to determine the output path for a plot.

    Args:
        filename: The filename for the plot (e.g., 'losses.png')
        output_path: Explicit output path (takes precedence)
        model_run_uuid: Model run UUID for centralized path generation

    Returns:
        The output path to use, or None if no path should be used (show plot instead)
    """
    if output_path:
        # Ensure the directory exists for explicit paths
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        return output_path
    if model_run_uuid:
        run_paths = get_run_paths(model_run_uuid)
        # Ensure the plots directory exists
        run_paths.plots.mkdir(parents=True, exist_ok=True)
        return run_paths.plot_file(filename)
    return None


# Configuration functions for testing
def set_runs_base_dir(new_base_dir: PathLike) -> None:
    """Update the base directory for runs storage."""
    _artifact_config.set_runs_base(new_base_dir)


def get_runs_base_dir() -> Path:
    """Get the current base directory for runs storage."""
    return _artifact_config.runs_base


def set_dataset_images_base_dir(new_base_dir: PathLike) -> None:
    """Update the base directory for dataset storage."""
    _artifact_config.set_datasets_base(new_base_dir)


def get_dataset_images_base_dir() -> Path:
    """Get the current base directory for dataset storage."""
    return _artifact_config.datasets_base


# Backward compatibility functions (delegate to new path managers)
def get_run_dir(model_run_uuid: str) -> Path:
    """Get the directory path for a specific model run."""
    return get_run_paths(model_run_uuid).base


def get_logs_dir(model_run_uuid: str) -> Path:
    """Get the logs directory path for a specific model run."""
    return get_run_paths(model_run_uuid).logs


def get_metrics_dir(model_run_uuid: str) -> Path:
    """Get the metrics directory path for a specific model run."""
    return get_run_paths(model_run_uuid).metrics


def get_metrics_summary_path(model_run_uuid: str) -> Path:
    """Get the path to the metrics summary file for a specific model run."""
    return get_run_paths(model_run_uuid).metrics_summary


def get_metrics_history_path(model_run_uuid: str) -> Path:
    """Get the path to the metrics history file for a specific model run."""
    return get_run_paths(model_run_uuid).metrics_history


def get_plots_dir(model_run_uuid: str) -> Path:
    """Get the plots directory path for a specific model run."""
    return get_run_paths(model_run_uuid).plots


def get_checkpoints_dir(model_run_uuid: str) -> Path:
    """Get the checkpoints directory path for a specific model run."""
    return get_run_paths(model_run_uuid).checkpoints


def get_augmentations_dir(model_run_uuid: str) -> Path:
    """Get the augmentations directory path for a specific model run."""
    return get_run_paths(model_run_uuid).augmentations


def get_labels_dir(model_run_uuid: str) -> Path:
    """Get the labels directory path for a specific model run."""
    return get_run_paths(model_run_uuid).labels


def get_model_path(model_run_uuid: str) -> Path:
    """Get the path to the final model file for a specific model run."""
    return get_run_paths(model_run_uuid).model_file


def get_checkpoint_path(model_run_uuid: str, epoch: Optional[int] = None) -> Path:
    """Get the path to a checkpoint file for a specific model run."""
    return get_run_paths(model_run_uuid).checkpoint_file(epoch)


def get_training_log_path(model_run_uuid: str) -> Path:
    """Get the path to the training log file for a specific model run."""
    return get_run_paths(model_run_uuid).training_log


def ensure_run_directories(model_run_uuid: str) -> Dict[str, Path]:
    """Create all necessary directories for a model run and return their paths."""
    return get_run_paths(model_run_uuid).ensure_directories()


def get_dataset_dir(dataset_uuid: str, content_updated_at: Optional[str] = None) -> Path:
    """Get the directory path for a specific dataset."""
    return get_dataset_paths(dataset_uuid, content_updated_at).base


def get_dataset_images_dir(dataset_uuid: str, content_updated_at: Optional[str] = None) -> Path:
    """Get the images directory path for a specific dataset."""
    return get_dataset_paths(dataset_uuid, content_updated_at).images


def get_dataset_coin_side_dir(
    dataset_uuid: str, coin_side_uuid: str, content_updated_at: Optional[str] = None
) -> Path:
    """Get the directory path for a specific coin side within a dataset."""
    return get_dataset_paths(dataset_uuid, content_updated_at).coin_side_dir(coin_side_uuid)


def get_dataset_image_path(
    dataset_uuid: str,
    coin_side_uuid: str,
    image_uuid: str,
    content_updated_at: Optional[str] = None,
    extension: str = "jpg",
) -> Path:
    """Get the full path for a specific image file."""
    return get_dataset_paths(dataset_uuid, content_updated_at).image_path(
        coin_side_uuid, image_uuid, extension
    )


def get_dataset_logs_dir(dataset_uuid: str, content_updated_at: Optional[str] = None) -> Path:
    """Get the logs directory path for a specific dataset."""
    return get_dataset_paths(dataset_uuid, content_updated_at).logs


def ensure_dataset_directories(
    dataset_uuid: str, content_updated_at: Optional[str] = None
) -> Dict[str, Path]:
    """Create all necessary directories for a dataset and return their paths."""
    return get_dataset_paths(dataset_uuid, content_updated_at).ensure_directories()
