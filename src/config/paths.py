"""
Configuration module for file and directory paths used throughout the application.
"""

from datetime import datetime
from pathlib import Path
from typing import Optional, Union

# Type alias for path-like objects
PathLike = Union[str, Path]

# Base directories
ARTIFACTS_DIR = Path("artifacts")
RUNS_DIR = ARTIFACTS_DIR / "runs"
DATASETS_BASE_DIR = ARTIFACTS_DIR / "datasets"
LOGS_DIR = "logs"
METRICS_DIR = "metrics"
PLOTS_DIR = "plots"
CHECKPOINTS_DIR = "checkpoints"
AUGMENTATIONS_DIR = "augmentations"
MANUAL_TEST_OUTPUT_DIR = Path("tests/manual/outcome")

# Dataset-specific directories
BACKGROUND_DIR = "background"
DIR_STATIC = "static/"
DIR_COINS = f"{DIR_STATIC}coins"
DIR_FACTS = f"{DIR_STATIC}facts"
DIR_FACTS_COINS = f"{DIR_FACTS}/coins"
DIR_FACTS_BANKNOTES = f"{DIR_FACTS}/banknotes"
DIR_FACTS_MISC = f"{DIR_FACTS}/miscellaneous"
DIR_FACTS_STAMPS = f"{DIR_FACTS}/stamps"
DIR_FACTS_WATCH = f"{DIR_FACTS}/watches"
DIR_SCRAPES = f"{DIR_STATIC}scrapes"


class DatasetConfig:
    """Configuration class for dataset storage paths."""

    def __init__(self):
        self._base_dir = DATASETS_BASE_DIR

    def set_base_dir(self, new_base_dir: PathLike) -> None:
        """Set the base directory for dataset image storage."""
        self._base_dir = Path(new_base_dir)

    def get_base_dir(self) -> Path:
        """Get the current base directory for dataset image storage."""
        return self._base_dir


class RunsConfig:
    """Configuration class for runs storage paths."""

    def __init__(self):
        self._base_dir = RUNS_DIR

    def set_base_dir(self, new_base_dir: PathLike) -> None:
        """Set the base directory for runs storage."""
        self._base_dir = Path(new_base_dir)

    def get_base_dir(self) -> Path:
        """Get the current base directory for runs storage."""
        return self._base_dir


# Global configuration instances
_dataset_config = DatasetConfig()
_runs_config = RunsConfig()

# File names
METRICS_SUMMARY_FILE = "summary.json"
METRICS_HISTORY_FILE = "history.json"
TRAINING_LOG_FILE_TEMPLATE = "training.log"
MODEL_FILE = "model.pt"
CHECKPOINT_FILE_TEMPLATE = "checkpoint_epoch_{epoch}.pt"
BEST_MODEL_FILE = "best_model.pt"


# Path construction helpers
def get_run_dir(model_run_uuid: str) -> Path:
    """
    Get the directory path for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        The path to the run directory.
    """
    return _runs_config.get_base_dir() / model_run_uuid


def get_logs_dir(model_run_uuid: str) -> Path:
    """
    Get the logs directory path for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        The path to the logs directory.
    """
    return get_run_dir(model_run_uuid) / LOGS_DIR


def get_metrics_dir(model_run_uuid: str) -> Path:
    """
    Get the metrics directory path for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        The path to the metrics directory.
    """
    return get_run_dir(model_run_uuid) / METRICS_DIR


def get_metrics_summary_path(model_run_uuid: str) -> Path:
    """
    Get the path to the metrics summary file for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        The path to the metrics summary file.
    """
    return get_metrics_dir(model_run_uuid) / METRICS_SUMMARY_FILE


def get_metrics_history_path(model_run_uuid: str) -> Path:
    """
    Get the path to the metrics history file for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        The path to the metrics history file.
    """
    return get_metrics_dir(model_run_uuid) / METRICS_HISTORY_FILE


def get_plots_dir(model_run_uuid: str) -> Path:
    """
    Get the plots directory path for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        The path to the plots directory.
    """
    return get_run_dir(model_run_uuid) / PLOTS_DIR


def get_manual_test_output_dir(test_name: Optional[str] = None) -> Path:
    """
    Get the output directory path for manual tests.

    Args:
        test_name: Optional name of the specific test (e.g., 'augmentation_inspection')

    Returns:
        The path to the manual test output directory.
    """
    if test_name:
        return MANUAL_TEST_OUTPUT_DIR / test_name
    return MANUAL_TEST_OUTPUT_DIR


def get_checkpoints_dir(model_run_uuid: str) -> Path:
    """
    Get the checkpoints directory path for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        The path to the checkpoints directory.
    """
    return get_run_dir(model_run_uuid) / CHECKPOINTS_DIR


def get_augmentations_dir(model_run_uuid: str) -> Path:
    """
    Get the augmentations directory path for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        The path to the augmentations directory.
    """
    return get_run_dir(model_run_uuid) / AUGMENTATIONS_DIR


def get_background_dir_name() -> str:
    """
    Get the background directory name for unlabeled images.

    Returns:
        The name of the background directory.
    """
    return BACKGROUND_DIR


def get_labels_dir(model_run_uuid: str) -> Path:
    """
    Get the labels directory path for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        The path to the labels directory.
    """
    return get_run_dir(model_run_uuid) / "labels"


def get_model_path(model_run_uuid: str) -> Path:
    """
    Get the path to the final model file for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        The path to the final model file.
    """
    return get_run_dir(model_run_uuid) / MODEL_FILE


def get_checkpoint_path(model_run_uuid: str, epoch: Optional[int] = None) -> Path:
    """
    Get the path to a checkpoint file for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.
        epoch: Optional epoch number for specific checkpoint. If None, returns best model path.

    Returns:
        The path to the checkpoint file.
    """
    checkpoints_dir = get_checkpoints_dir(model_run_uuid)
    if epoch is None:
        return checkpoints_dir / BEST_MODEL_FILE
    return checkpoints_dir / CHECKPOINT_FILE_TEMPLATE.format(epoch=epoch)


def get_training_log_path(model_run_uuid: str) -> Path:
    """
    Get the path to the training log file for a specific model run.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        The path to the training log file.
    """
    return get_logs_dir(model_run_uuid) / TRAINING_LOG_FILE_TEMPLATE


def ensure_run_directories(model_run_uuid: str) -> dict[str, Path]:
    """
    Create all necessary directories for a model run and return their paths.

    Args:
        model_run_uuid: The UUID of the model run.

    Returns:
        Dictionary mapping directory types to their paths.
    """
    directories = {
        "run": get_run_dir(model_run_uuid),
        "logs": get_logs_dir(model_run_uuid),
        "metrics": get_metrics_dir(model_run_uuid),
        "plots": get_plots_dir(model_run_uuid),
        "checkpoints": get_checkpoints_dir(model_run_uuid),
        "augmentations": get_augmentations_dir(model_run_uuid),
        "labels": get_labels_dir(model_run_uuid),
    }

    # Create all directories
    for dir_path in directories.values():
        dir_path.mkdir(parents=True, exist_ok=True)

    return directories


def get_plot_path(
    filename: str,
    output_path: Optional[PathLike] = None,
    model_run_uuid: Optional[str] = None,
) -> Optional[Path]:
    """
    Helper function to determine the output path for a plot.

    Args:
        filename: The filename for the plot (e.g., 'losses.png')
        output_path: Explicit output path (takes precedence)
        model_run_uuid: Model run UUID for centralized path generation

    Returns:
        The output path to use, or None if no path should be used (show plot instead)
    """
    if output_path:
        # Ensure the directory exists for explicit paths
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        return output_path
    if model_run_uuid:
        plots_dir = get_plots_dir(model_run_uuid)
        # Ensure the plots directory exists
        plots_dir.mkdir(parents=True, exist_ok=True)
        return plots_dir / filename
    return None


# Dataset path management functions
def get_dataset_dir(
    dataset_uuid: str, content_updated_at: Optional[str] = None
) -> Path:
    """
    Get the directory path for a specific dataset.

    Args:
        dataset_uuid: The UUID of the dataset
        content_updated_at: Optional content update timestamp for versioning

    Returns:
        The path to the dataset directory
    """
    if content_updated_at:
        # Format: datasets/{dataset_uuid}_{unix_timestamp}
        # Convert timestamp to Unix timestamp for filesystem safety
        try:
            # Parse the timestamp and convert to Unix timestamp
            dt = datetime.fromisoformat(content_updated_at.replace("Z", "+00:00"))
            unix_timestamp = int(dt.timestamp())
            dataset_name = f"{dataset_uuid}_{unix_timestamp}"
        except (ValueError, AttributeError):
            # Fallback to UUID only if timestamp parsing fails
            dataset_name = dataset_uuid
    else:
        dataset_name = dataset_uuid

    base_dir = _dataset_config.get_base_dir()
    result = base_dir / dataset_name
    return result


def get_dataset_images_dir(
    dataset_uuid: str, content_updated_at: Optional[str] = None
) -> Path:
    """
    Get the images directory path for a specific dataset.

    Args:
        dataset_uuid: The UUID of the dataset
        content_updated_at: Optional content update timestamp for versioning

    Returns:
        The path to the dataset images directory
    """
    return get_dataset_dir(dataset_uuid, content_updated_at) / "images"


def get_dataset_coin_side_dir(
    dataset_uuid: str, coin_side_uuid: str, content_updated_at: Optional[str] = None
) -> Path:
    """
    Get the directory path for a specific coin side within a dataset.

    Args:
        dataset_uuid: The UUID of the dataset
        coin_side_uuid: The UUID of the coin side
        content_updated_at: Optional content update timestamp for versioning

    Returns:
        The path to the coin side directory
    """
    images_dir = get_dataset_images_dir(dataset_uuid, content_updated_at)
    return images_dir / coin_side_uuid


def get_dataset_image_path(
    dataset_uuid: str,
    coin_side_uuid: str,
    image_uuid: str,
    content_updated_at: Optional[str] = None,
    extension: str = "jpg",
) -> Path:
    """
    Get the full path for a specific image file.

    Args:
        dataset_uuid: The UUID of the dataset
        coin_side_uuid: The UUID of the coin side
        image_uuid: The UUID of the image
        content_updated_at: Optional content update timestamp for versioning
        extension: File extension (default: jpg)

    Returns:
        The full path to the image file
    """
    coin_side_dir = get_dataset_coin_side_dir(
        dataset_uuid, coin_side_uuid, content_updated_at
    )
    return coin_side_dir / f"{image_uuid}.{extension}"


def get_dataset_logs_dir(
    dataset_uuid: str, content_updated_at: Optional[str] = None
) -> Path:
    """
    Get the logs directory path for a specific dataset.

    Args:
        dataset_uuid: The UUID of the dataset
        content_updated_at: Optional content update timestamp for versioning

    Returns:
        The path to the dataset logs directory
    """
    return get_dataset_dir(dataset_uuid, content_updated_at) / LOGS_DIR


def ensure_dataset_directories(
    dataset_uuid: str, content_updated_at: Optional[str] = None
) -> dict[str, Path]:
    """
    Create all necessary directories for a dataset and return their paths.

    Args:
        dataset_uuid: The UUID of the dataset
        content_updated_at: Optional content update timestamp for versioning

    Returns:
        Dictionary mapping directory types to their paths
    """
    directories = {
        "dataset": get_dataset_dir(dataset_uuid, content_updated_at),
        "images": get_dataset_images_dir(dataset_uuid, content_updated_at),
        "logs": get_dataset_logs_dir(dataset_uuid, content_updated_at),
    }

    # Create all directories
    for dir_path in directories.values():
        dir_path.mkdir(parents=True, exist_ok=True)

    return directories


def set_dataset_images_base_dir(new_base_dir: PathLike) -> None:
    """
    Update the base directory for dataset image storage.

    This allows for configurable storage location that can be updated
    when the actual image storage location is determined.

    Args:
        new_base_dir: New base directory path for dataset images
    """
    _dataset_config.set_base_dir(new_base_dir)


def get_dataset_images_base_dir() -> Path:
    """
    Get the current base directory for dataset image storage.

    Returns:
        The current base directory path for dataset images
    """
    return _dataset_config.get_base_dir()


def set_runs_base_dir(new_base_dir: PathLike) -> None:
    """
    Update the base directory for runs storage.

    This allows for configurable storage location that can be updated
    when the actual runs storage location is determined.

    Args:
        new_base_dir: New base directory path for runs
    """
    _runs_config.set_base_dir(new_base_dir)


def get_runs_base_dir() -> Path:
    """
    Get the current base directory for runs storage.

    Returns:
        The current base directory path for runs
    """
    return _runs_config.get_base_dir()
