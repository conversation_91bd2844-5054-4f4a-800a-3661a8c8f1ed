"""
DataLoadingService for creating data loaders from real image datasets.

This service handles loading images from the local filesystem based on dataset_sets
data from the database, creating proper train/validation/test splits, and applying
augmentation transforms.
"""

import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from uuid import UUID

from torch.utils.data import DataLoader
from torchvision import transforms

from augmentations.factory import AugmentationPipelineFactory
from config.logging_config import setup_dataset_logger
from config.paths import get_background_dir_name, get_dataset_images_dir
from database.models.dataset_set import DatasetSet, SetType
from database.services.dataset_service import DatasetService
from database.services.training_data_service import TrainingDataService
from datasets.augmented_dataset import AugmentedDataset
from datasets.image_dataset import ImageDataset, ImageDatasetConfig

# Logger for this module
logger = logging.getLogger(__name__)


@dataclass
class BackgroundClassConfig:
    """Configuration for background class handling."""

    include_background_class: bool = True
    background_class_name: str = get_background_dir_name()


@dataclass
class ImageDatasetParams:
    """Parameters for creating an ImageDataset."""

    dataset_sets: List
    dataset_uuid: Union[str, UUID]
    content_updated_at: Optional[str]
    set_name: str
    config: "DataLoaderConfig"
    base_dir: Optional[Union[str, Path]] = None


@dataclass
class TestIsolationConfig:
    """Configuration for test isolation parameters."""

    base_dir: Optional[Union[str, Path]] = None
    runs_base_dir: Optional[Union[str, Path]] = None


@dataclass
class DataSplitConfig:
    """Configuration for data splitting and validation."""

    test_size: float = 0.2
    validation_size: float = 0.1
    include_validation: bool = True


@dataclass
class DataProcessingConfig:
    """Configuration for data processing and loading."""

    batch_size: int = 32
    dataset_batch_size: int = 1000  # Batch size for fetching dataset sets from database
    use_mock_data: bool = False


@dataclass
class FormatConfig:
    """Configuration for image format handling."""

    image_config: Optional["ImageDatasetConfig"] = None
    format_validation: bool = True
    performance_monitoring: bool = False


@dataclass
class DataLoaderConfig:
    """Configuration for data loader creation."""

    model_run_uuid: str
    profile: Optional[str] = None
    # Configuration objects
    split_config: DataSplitConfig = field(default_factory=DataSplitConfig)
    processing_config: DataProcessingConfig = field(
        default_factory=DataProcessingConfig
    )
    background_config: BackgroundClassConfig = field(
        default_factory=BackgroundClassConfig
    )
    test_config: TestIsolationConfig = field(default_factory=TestIsolationConfig)
    # Image format configuration
    format_config: FormatConfig = field(default_factory=FormatConfig)


class DataLoadingError(Exception):
    """Exception raised for data loading errors."""


class DataLoadingService:
    """Service for creating data loaders from real image datasets."""

    # Threshold for dataset completeness - if below this percentage, training will fail
    COMPLETENESS_THRESHOLD = 0.95  # 95% of images must be available

    @classmethod
    async def verify_dataset_completeness(
        cls,
        dataset_uuid: Union[str, UUID],
        config: DataLoaderConfig,
        auto_recover: bool = True,
    ) -> Dict:
        """
        Verify that all images in a dataset are available locally and attempt recovery if needed.

        This method performs the following steps:
        1. Checks if all expected images are available locally
        2. If auto_recover is True, attempts to download missing images (up to 2 attempts)
        3. Verifies that the dataset meets the completeness threshold

        Args:
            dataset_uuid: UUID of the dataset to verify
            config: Configuration containing model run UUID, profile, and test isolation settings
            auto_recover: Whether to automatically attempt to recover missing images

        Returns:
            Dictionary containing verification results

        Raises:
            DataLoadingError: If dataset completeness is below the threshold after recovery
        """
        # Set up dataset logger for this model run
        dataset_logger = setup_dataset_logger(
            config.model_run_uuid, runs_base_dir=config.test_config.runs_base_dir
        )

        # Get the base directory for dataset images
        if config.test_config.base_dir:
            # Use custom base directory for tests
            images_base_dir = (
                Path(config.test_config.base_dir) / dataset_uuid / "images"
            )
        else:
            # Use default production directory
            images_base_dir = get_dataset_images_dir(dataset_uuid)

        try:
            dataset_logger.info("Verifying dataset completeness for %s", dataset_uuid)
            if not auto_recover:
                dataset_logger.info(
                    "Auto-recovery is disabled, will only check completeness"
                )

            # Verify dataset completeness and recover missing images if needed
            verification_result = (
                await DatasetService.verify_and_recover_dataset_images(
                    dataset_uuid=dataset_uuid,
                    base_output_dir=images_base_dir,
                    profile=config.profile,
                    auto_recover=auto_recover,
                )
            )

            # Calculate completeness percentage
            expected_count = verification_result["expected_count"]
            available_count = verification_result["available_count"]

            if expected_count == 0:
                dataset_logger.warning(
                    "Dataset %s has no expected images", dataset_uuid
                )
                return verification_result

            completeness = available_count / expected_count
            verification_result["completeness"] = completeness

            # Check if dataset meets completeness threshold
            if completeness < cls.COMPLETENESS_THRESHOLD:
                missing_count = verification_result["missing_count"]
                dataset_logger.error(
                    "Dataset %s completeness (%.2f%%) is below threshold (%.2f%%). "
                    "%d images are still missing.",
                    dataset_uuid,
                    completeness * 100,
                    cls.COMPLETENESS_THRESHOLD * 100,
                    missing_count,
                )
                raise DataLoadingError(
                    f"Dataset is incomplete: {available_count}/{expected_count} images available. "
                    f"({completeness:.2%}) is below required ({cls.COMPLETENESS_THRESHOLD:.2%})."
                )

            dataset_logger.info(
                "Dataset %s verification successful: %d/%d images available (%.2f%%)",
                dataset_uuid,
                available_count,
                expected_count,
                completeness * 100,
            )

            return verification_result

        except Exception as e:
            dataset_logger.error("Error verifying dataset completeness: %s", str(e))
            raise DataLoadingError(
                f"Failed to verify dataset completeness: {str(e)}"
            ) from e

    @classmethod
    async def create_data_loaders(
        cls,
        dataset_uuid: Union[str, UUID],
        augmentations: Optional[List[Any]] = None,
        config: Optional[DataLoaderConfig] = None,
    ) -> Dict[str, DataLoader]:
        """
        Create data loaders for training, testing, and validation from a dataset.

        Args:
            dataset_uuid: UUID of the dataset to load
            augmentations: Optional list of augmentations to apply
            config: Configuration for data loader creation (batch size, splits, etc.)

        Returns:
            Dictionary containing 'train', 'test', and optionally 'validation'
            data loaders

        Raises:
            DataLoadingError: If data loading fails
        """
        # pylint: disable=too-many-locals
        # Use default config if none provided
        if config is None:
            raise DataLoadingError("DataLoaderConfig with model_run_uuid is required")

        # Set up dataset logger for this specific model run
        if not config.model_run_uuid:
            raise DataLoadingError("model_run_uuid is required for dataset logging")

        dataset_logger = setup_dataset_logger(
            config.model_run_uuid, runs_base_dir=config.test_config.runs_base_dir
        )

        try:
            if config.processing_config.use_mock_data:
                logger.error(
                    "Mock data requested for dataset %s, but mock data is not "
                    "supported in production",
                    dataset_uuid,
                )
                raise DataLoadingError(
                    "Mock data is not supported in production. Please provide a valid dataset."
                )

            # Verify dataset completeness and attempt recovery if needed
            await cls.verify_dataset_completeness(
                dataset_uuid=dataset_uuid,
                config=config,
                auto_recover=True,
            )

            # Get dataset information
            dataset_info = DatasetService.get_dataset(dataset_uuid, config.profile)
            content_updated_at = dataset_info.get("content_updated_at")

            # Load dataset sets from database
            train_sets, validation_sets, test_sets = cls._load_dataset_sets(
                dataset_uuid,
                config.profile,
                config.processing_config.dataset_batch_size,
            )

            # Create image datasets
            train_params = ImageDatasetParams(
                dataset_sets=train_sets,
                dataset_uuid=dataset_uuid,
                content_updated_at=content_updated_at,
                set_name="train",
                config=config,
                base_dir=config.test_config.base_dir,
            )
            train_dataset = cls._create_image_dataset(train_params)

            # Handle validation sets
            validation_dataset = None
            if config.split_config.include_validation and validation_sets:
                validation_params = ImageDatasetParams(
                    dataset_sets=validation_sets,
                    dataset_uuid=dataset_uuid,
                    content_updated_at=content_updated_at,
                    set_name="validation",
                    config=config,
                    base_dir=config.test_config.base_dir,
                )
                validation_dataset = cls._create_image_dataset(validation_params)

            # Use test sets if available, otherwise split from training data
            if test_sets:
                test_params = ImageDatasetParams(
                    dataset_sets=test_sets,
                    dataset_uuid=dataset_uuid,
                    content_updated_at=content_updated_at,
                    set_name="test",
                    config=config,
                    base_dir=config.test_config.base_dir,
                )
                test_dataset = cls._create_image_dataset(test_params)
            else:
                # Split training data for testing
                train_dataset, test_dataset = cls._split_dataset(
                    train_dataset, config.split_config.test_size
                )

                # If no explicit validation set and we want validation, split from training
                if (
                    config.split_config.include_validation
                    and not validation_sets
                    and config.split_config.validation_size > 0
                ):
                    train_dataset, validation_dataset = cls._split_dataset(
                        train_dataset, config.split_config.validation_size
                    )

            # Apply augmentations if provided
            if augmentations:
                train_transforms, test_transforms = cls._create_transform_pipelines(
                    augmentations
                )
                train_dataset = AugmentedDataset(
                    train_dataset.images, train_dataset.labels, train_transforms
                )
                test_dataset = AugmentedDataset(
                    test_dataset.images, test_dataset.labels, test_transforms
                )
                if validation_dataset:
                    validation_dataset = AugmentedDataset(
                        validation_dataset.images,
                        validation_dataset.labels,
                        test_transforms,
                    )

            # Create data loaders
            train_loader = DataLoader(
                train_dataset,
                batch_size=config.processing_config.batch_size,
                shuffle=True,
                drop_last=True,
            )
            test_loader = DataLoader(
                test_dataset,
                batch_size=config.processing_config.batch_size,
                shuffle=False,
            )

            result = {"train": train_loader, "test": test_loader}

            # Add validation loader if available
            if validation_dataset:
                validation_loader = DataLoader(
                    validation_dataset,
                    batch_size=config.processing_config.batch_size,
                    shuffle=False,
                )
                result["validation"] = validation_loader

            dataset_logger.info(
                "Created data loaders for dataset %s: Train=%d samples, Test=%d samples%s",
                dataset_uuid,
                len(train_dataset),
                len(test_dataset),
                (
                    f", Validation={len(validation_dataset)} samples"
                    if validation_dataset
                    else ""
                ),
            )

            return result

        except Exception as e:
            dataset_logger.error(
                "Failed to create data loaders for dataset %s: %s", dataset_uuid, str(e)
            )
            raise DataLoadingError(f"Failed to create data loaders: {str(e)}") from e

    @classmethod
    def _load_dataset_sets(
        cls,
        dataset_uuid: Union[str, UUID],
        profile: Optional[str] = None,
        dataset_batch_size: int = 1000,
    ) -> Tuple[List[DatasetSet], List[DatasetSet], List[DatasetSet]]:
        """
        Load dataset sets from database, organized by set type.

        Args:
            dataset_uuid: UUID of the dataset
            profile: Optional Supabase profile name
            dataset_batch_size: Batch size for fetching dataset sets from database

        Returns:
            Tuple of (train_sets, validation_sets, test_sets)
        """
        train_sets = []
        validation_sets = []
        test_sets = []

        # Load all dataset sets in batches
        for batch in DatasetService.get_dataset_sets(
            dataset_uuid=dataset_uuid,
            profile=profile,
            batch_size=dataset_batch_size,
        ):
            for dataset_set in batch:
                if dataset_set.set_type == SetType.TRAIN:
                    train_sets.append(dataset_set)
                elif dataset_set.set_type == SetType.VALIDATION:
                    validation_sets.append(dataset_set)
                elif dataset_set.set_type == SetType.TEST:
                    test_sets.append(dataset_set)

        logger.info(
            "Loaded dataset sets for %s: Train=%d, Validation=%d, Test=%d",
            dataset_uuid,
            len(train_sets),
            len(validation_sets),
            len(test_sets),
        )

        return train_sets, validation_sets, test_sets

    @classmethod
    def _create_image_dataset(cls, params: ImageDatasetParams) -> ImageDataset:
        """
        Create an ImageDataset from dataset sets.

        Args:
            params: ImageDatasetParams containing all necessary parameters

        Returns:
            ImageDataset instance
        """
        if not params.dataset_sets:
            raise DataLoadingError(f"No {params.set_name} dataset sets found")

        # Get the base images directory
        if params.base_dir:
            # Use custom base directory for tests
            images_base_dir = Path(params.base_dir) / params.dataset_uuid / "images"
        else:
            # Use default production directory
            images_base_dir = get_dataset_images_dir(
                str(params.dataset_uuid), params.content_updated_at
            )

        # Create image dataset config with enhanced format and background class settings
        if params.config.format_config.image_config:
            # Use provided image config as base
            dataset_config = params.config.format_config.image_config
            # Override background class settings from main config
            dataset_config.include_background_class = (
                params.config.background_config.include_background_class
            )
            dataset_config.background_class_name = (
                params.config.background_config.background_class_name
            )
        else:
            # Create default multi-format config with background class settings
            dataset_config = ImageDatasetConfig(
                include_background_class=params.config.background_config.include_background_class,
                background_class_name=params.config.background_config.background_class_name,
            )

        # Create image dataset
        dataset = ImageDataset(params.dataset_sets, images_base_dir, dataset_config)

        # Perform format validation if enabled
        if params.config.format_config.format_validation:
            validation_results = dataset.validate_dataset_formats()
            cls._log_format_validation_results(validation_results, params.set_name)

        logger.info(
            "Created %s dataset with %d images from %s (background class: %s)",
            params.set_name,
            len(dataset),
            images_base_dir,
            (
                "enabled"
                if params.config.background_config.include_background_class
                else "disabled"
            ),
        )

        return dataset

    @classmethod
    def _log_format_validation_results(cls, results: Dict[str, Any], set_name: str):
        """Log format validation results with enhanced details."""
        logger.info("Format validation for %s dataset:", set_name)
        logger.info("  Format distribution: %s", results["format_distribution"])

        # Log health score if available
        if "health_score" in results:
            health_score = results["health_score"]
            if health_score >= 0.9:
                logger.info("  Dataset health: EXCELLENT (%.1f%%)", health_score * 100)
            elif health_score >= 0.7:
                logger.info("  Dataset health: GOOD (%.1f%%)", health_score * 100)
            elif health_score >= 0.5:
                logger.warning("  Dataset health: FAIR (%.1f%%)", health_score * 100)
            else:
                logger.error("  Dataset health: POOR (%.1f%%)", health_score * 100)

        if results["corrupted_files"]:
            logger.warning(
                "  Found %d corrupted files", len(results["corrupted_files"])
            )
            # Log first few corrupted files for debugging
            for corrupted in results["corrupted_files"][:3]:
                logger.warning("    - %s: %s", corrupted["path"], corrupted["error"])
            if len(results["corrupted_files"]) > 3:
                logger.warning(
                    "    ... and %d more", len(results["corrupted_files"]) - 3
                )

        if results["unsupported_formats"]:
            logger.warning(
                "  Found %d unsupported formats", len(results["unsupported_formats"])
            )

        if results["large_files"]:
            logger.warning(
                "  Found %d large files (>10MB)", len(results["large_files"])
            )

        # Log recommendations if available
        if "recommendations" in results and results["recommendations"]:
            logger.info("  Recommendations:")
            for recommendation in results["recommendations"]:
                logger.info("    - %s", recommendation)

    @classmethod
    def _split_dataset(
        cls, dataset: ImageDataset, split_ratio: float
    ) -> Tuple[ImageDataset, ImageDataset]:
        """
        Split a dataset into train and test portions.

        Note: This method is only used as a fallback when explicit test sets
        are not available in the database. The preferred approach is to use
        dataset sets with set_type = TEST from the database.

        Args:
            dataset: Dataset to split
            split_ratio: Fraction to use for the second split (validation/test)

        Returns:
            Tuple of (train_dataset, test_dataset)
        """
        # This is a placeholder - in practice, you'd implement proper splitting
        # For now, we'll use the same dataset for both (this should be improved)
        logger.warning(
            "Dataset splitting not fully implemented - using same data for train/test. "
            "Consider using explicit dataset sets with set_type in the database. "
            "Split ratio: %.2f",
            split_ratio,
        )
        return dataset, dataset

    @classmethod
    def _create_transform_pipelines(
        cls, augmentations: List[Any]
    ) -> Tuple[transforms.Compose, transforms.Compose]:
        """
        Create transform pipelines from augmentations.

        Args:
            augmentations: List of augmentation configurations

        Returns:
            Tuple of (train_transforms, test_transforms)
        """
        # Create proper training and inference pipelines
        train_transforms = AugmentationPipelineFactory.create_training_pipeline(
            augmentations
        )
        test_transforms = AugmentationPipelineFactory.create_inference_pipeline(
            augmentations
        )

        logger.info(
            "Created augmentation pipelines - Train: %d transforms, Test: %d transforms",
            len(train_transforms.transforms),
            len(test_transforms.transforms),
        )
        return train_transforms, test_transforms

    @classmethod
    async def create_data_loaders_with_cache_optimization(
        cls,
        training_data: Dict,
        augmentations: Optional[List[Any]] = None,
        config: Optional[DataLoaderConfig] = None,
    ) -> Dict[str, DataLoader]:
        """
        Create data loaders with cache optimization based on training data cache state.

        This method checks the cache state from training data and skips dataset_sets
        fetching if the dataset hasn't changed since the last preparation.

        It also verifies dataset completeness and attempts recovery for missing images
        before creating data loaders.

        Args:
            training_data: Training data dictionary containing cache state
            augmentations: Optional list of augmentation transforms
            config: Optional configuration for data loader creation

        Returns:
            Dictionary containing train, validation (optional), and test data loaders

        Raises:
            DataLoadingError: If there's an error creating the data loaders
        """
        if config is None:
            raise DataLoadingError("DataLoaderConfig with model_run_uuid is required")

        # Check for required model_run_uuid
        if not config.model_run_uuid:
            raise DataLoadingError("model_run_uuid is required for dataset logging")

        # Extract dataset information from training data
        dataset_info = training_data.get("dataset", {})
        dataset_uuid = dataset_info.get("uuid")

        if not dataset_uuid:
            raise DataLoadingError("Dataset UUID not found in training data")

        # Always verify dataset completeness, regardless of cache state
        # This ensures all images are available locally before training
        try:
            verification_result = await cls.verify_dataset_completeness(
                dataset_uuid=dataset_uuid,
                config=config,
                auto_recover=True,
            )
            logger.info(
                "Dataset verification complete: %d/%d images available (%.2f%%)",
                verification_result["available_count"],
                verification_result["expected_count"],
                verification_result.get("completeness", 0) * 100,
            )
        except DataLoadingError as e:
            # If verification fails, we cannot proceed with training
            logger.error("Dataset verification failed: %s", str(e))
            raise

        # Check if we can skip dataset preparation based on cache state
        cache_state = training_data.get("cache_state", {})
        should_skip = cache_state.get("dataset_preparation_skipped", False)

        if should_skip:
            logger.info(
                "Dataset preparation skipped for dataset %s (valid cache state cache_key: %s)",
                dataset_uuid,
                cache_state.get("cache_key", "unknown"),
            )
            # For now, we still need to create data loaders, but in the future
            # this could be optimized to use cached data loaders as well
            # Fall through to normal data loader creation

        # Create data loaders using the existing method
        data_loaders = await cls.create_data_loaders(
            dataset_uuid=dataset_uuid,
            augmentations=augmentations,
            config=config,
        )

        # Mark dataset preparation as complete if it wasn't skipped
        if not should_skip:
            content_updated_at = dataset_info.get("content_updated_at")
            TrainingDataService.mark_dataset_preparation_complete(
                dataset_uuid=dataset_uuid,
                content_updated_at=content_updated_at,
            )
            logger.info(
                "Dataset preparation completed and cached for dataset %s",
                dataset_uuid,
            )

        return data_loaders
