# Training Data Flow

This document describes the complete training data flow process in the coin classification system, from database retrieval to model training with real image data.

## Overview

The training data flow is a comprehensive pipeline that transforms database records into PyTorch data loaders for machine learning training:

```
Database   →   Dataset Preparation   →   Image Download   →   Data Loading   →   Training
    ↓                   ↓                      ↓                   ↓                ↓
Dataset Sets       Cache Check            Local Storage       Data Loaders     Model Training
```

### Complete Flow Process

1. **Dataset Preparation**: Retrieve dataset metadata from database with intelligent caching
2. **Image Acquisition**: Download and organize images locally with multi-format support
3. **Data Loading**: Create PyTorch data loaders with proper train/validation/test separation
4. **Training Execution**: Feed real data into the training pipeline with comprehensive evaluation

## Phase 1: Dataset Preparation

### Dataset Metadata Retrieval

The training data flow begins with retrieving dataset metadata from the database through the `TrainingDataService`:

**Key Components:**

- **TrainingDataService** (`src/database/services/training_data_service.py`): Orchestrates dataset preparation
- **DatasetStateCacheService** (`src/database/services/dataset_state_cache.py`): Provides intelligent caching
- **DatasetService** (`src/database/services/dataset_service.py`): Handles database interactions

**Process Flow:**

1. **Cache Check**: Verify if dataset preparation can be skipped using cache key `{dataset_uuid}_{content_updated_at}`
2. **Dataset Sets Retrieval**: Load dataset_sets from database with proper set type handling (TRAIN/VALIDATION/TEST)
3. **Cache Update**: Mark dataset as prepared to optimize future runs

### Intelligent Caching System

The system implements smart caching to avoid redundant database operations:

**Cache States:**

- **Prepared**: Dataset processed, can skip dataset_sets fetching
- **Unprepared**: Dataset needs processing
- **Invalid**: Cache entry outdated or missing

**Benefits:**

- Eliminates redundant database calls when dataset content unchanged
- Automatic invalidation when `content_updated_at` changes
- Thread-safe operations for production environments

## Phase 2: Image Acquisition

### Local Storage Organization

Images are downloaded and organized in a structured directory hierarchy:

**Directory Structure:**

```
datasets/
├── {dataset_uuid}_{content_updated_at}/
│   └── images/
│       ├── {coin_side_uuid_1}/
│       │   ├── {image_uuid_1}.{ext}
│       │   └── {image_uuid_2}.{ext}
│       ├── {coin_side_uuid_2}/
│       │   ├── {image_uuid_3}.{ext}
│       │   └── {image_uuid_4}.{ext}
│       └── background/    # For unlabeled images
│           └── {image_uuid_n}.{ext}
```

### Download Architecture

**Generic Downloader** (`src/database/utils/downloader.py`):

- Multi-protocol support (HTTP/HTTPS, local file copying)
- Automatic retry with exponential backoff
- Comprehensive error handling and recovery
- Session management for efficient batch downloads

**Dataset Downloader** (`src/database/utils/dataset_downloader.py`):

- Dataset-specific logic and path management
- Integration with DatasetSet objects
- Multi-format image support (JPEG, PNG, GIF, WebP, HEIC, HEVC)
- Intelligent recovery of missing images

### Multi-Format Image Support

The system supports comprehensive image format handling:

| Format   | Extensions | Support Level | Notes                     |
| -------- | ---------- | ------------- | ------------------------- |
| **JPEG** | jpg, jpeg  | ✅ Full        | Optimized performance    |
| **PNG**  | png        | ✅ Full        | Lossless, preserves details    |
| **GIF**  | gif        | ✅ Full        | Animated images with transparency |
| **WebP** | webp       | ✅ Full        | Modern format with transparency    |
| **HEIC/HEIF** | heic, heif | ✅ Full        | Mobile format (requires pillow-heif)    |
| **HEVC** | hevc       | ✅ Full        | Video codec still images       |

**Smart Format Detection:**

- URL detection with fallback attempts to find any supported format
- Performance optimization: Caches results for faster access

## Phase 3: Data Loading

### PyTorch Dataset Creation

The `ImageDataset` class (`src/datasets/image_dataset.py`) serves as the core PyTorch Dataset implementation:

**Core Functionality:**

- **Set Type Handling**: Properly separates TRAIN, VALIDATION, TEST data
- **Label Generation**: Automatically creates integer labels from coin_side_uuid
- **Multi-Format Loading**: Supports all image formats with format-specific optimizations
- **Memory Efficiency**: On-demand image loading with caching
- **Error Resilience**: Graceful handling of missing or corrupted images

### Label Generation and Classification

The system automatically generates consistent labels for classification:

**Label Creation Process:**

1. **Source Extraction**: Derives labels from `coin_side_uuid` values in DatasetSet records
2. **Consistent Mapping**: Sorts coin_side_uuid alphabetically for reproducible label assignment
3. **Background Support**: Optionally includes unlabeled images (`coin_side_uuid = null`) as background class
4. **Integer Conversion**: Maps coin_side_uuid to integer labels (0, 1, 2, ...)

**Label Configuration:**

```python
# Standard configuration with background class
config = ImageDatasetConfig(
    include_background_class=True,
    background_class_name="background"
)

# Exclude unlabeled images
config = ImageDatasetConfig(include_background_class=False)
```

**Label Assignment Example:**

- `{"coin-side-abc": 0, "coin-side-def": 1, "background": 2}`
- Background class always receives highest integer value

### Data Loader Creation

The `DataLoadingService` (`src/datasets/data_loading_service.py`) orchestrates PyTorch DataLoader creation:

**Key Features:**

- **Set Separation**: Creates separate loaders for train/validation/test data
- **Augmentation Integration**: Applies appropriate transforms per set type
- **Batch Configuration**: Handles batch size, shuffling, and worker processes
- **Validation Handling**: Creates validation loaders when available in database
- **Fallback Support**: Gracefully falls back to mock data if real data unavailable

**Data Splitting Strategy:**

1. **Explicit Sets Available**: Uses database-defined train/validation/test splits
2. **Partial Sets**: Uses available sets and intelligently splits training data for missing sets
3. **Training Only**: Splits training data into train/test, optionally creates validation set

### Format-Specific Optimizations

**Image Loading Optimizations:**

- **HEIC/HEIF**: Hardware acceleration when available via pillow-heif
- **WebP**: Proper transparency handling with background compositing
- **Standard Formats**: Optimized PIL loading for JPEG/PNG
- **Memory Management**: Pre-loading file size validation to prevent memory issues
- **Error Recovery**: Comprehensive error handling with actionable messages

**Performance Features:**

- **Smart Detection**: URL-based format detection with intelligent fallback
- **Thread Safety**: Safe for multi-worker PyTorch DataLoader environments
- **Performance Monitoring**: Tracks loading times and success rates by format
- **Health Scoring**: Quantitative dataset quality assessment (0.0-1.0 scale)

## Phase 4: Training Execution

### Training Pipeline Integration

The training data flow seamlessly integrates with the training pipeline through the `TrainingJobDispatcher`:

**Pre-Training Setup:**

1. **Dataset Validation**: Ensures images are available locally before training begins
2. **Automatic Download**: Downloads missing images using DatasetDownloader if needed
3. **Data Loader Creation**: Creates optimized PyTorch DataLoaders with proper set separation
4. **Augmentation Application**: Applies appropriate transforms based on set type

### Proper Train/Validation/Test Separation

The system implements correct machine learning data separation practices:

**Training Phase (Each Epoch):**

- **Training Data**: Used for model parameter updates with augmentations
- **Validation Data**: Used for epoch validation (if available) with deterministic transforms
- **Test Data Fallback**: Used for validation only when no dedicated validation set exists

**Smart Data Selection Logic:**

```python
# Intelligent validation data selection
validation_loader = self.data_loaders.get("validation", self.data_loaders["test"])
loader_type = "validation" if "validation" in self.data_loaders else "test"
```

**Final Evaluation Phase:**

- **Held-Out Test Evaluation**: Only when validation data was used during training
- **Comprehensive Metrics**: Unbiased performance assessment on truly unseen data
- **Skip Logic**: No final test evaluation if test data was used for epoch validation

### Label Artifacts and Model Compatibility

The system automatically generates comprehensive label artifacts during training:

**Generated Artifacts:**

- `label_mapping.json`: coin_side_uuid → integer mapping
- `class_names.json`: Ordered list of class names
- `reverse_label_mapping.json`: integer → coin_side_uuid mapping
- `label_metadata.json`: Comprehensive label information and statistics

**Architecture Compatibility:**
All supported architectures (CNN, ResNet, DenseNet, MobileNet, ViT) are compatible with the integer label format. The system automatically:

- Detects number of classes from dataset
- Validates model output size matches dataset classes
- Handles binary (2 classes → 1 output) and multi-class scenarios
- Ensures consistency between training and inference

## Configuration and Environment

### Configurable Storage Locations

The system supports flexible image storage configuration through environment variables:

**Environment Configuration:**

```bash
# .env.local
COINY_CLASSIFIER_IMAGES_BASE_DIR=/path/to/image/storage
```

**Path Management:**

- **Versioned Directories**: `{dataset_uuid}_{content_updated_at}` format
- **Organized Structure**: Hierarchical organization by coin_side_uuid
- **Configurable Base**: Environment-specific storage locations
- **Automatic Creation**: Ensures necessary directories exist

### Dataset Versioning

Images are organized with built-in versioning support:

- **Version Key**: `{dataset_uuid}_{content_updated_at}`
- **Cache Invalidation**: Automatic cache invalidation on content updates
- **Multiple Versions**: Supports multiple dataset versions simultaneously
- **Cleanup Strategy**: Future enhancement for automatic old version cleanup

## Complete Training Data Flow Benefits

### Production-Ready Features

**Real Data Training:**

- Models train on actual coin images instead of synthetic data
- Proper handling of real-world image variations and quality
- Support for mixed-format datasets with comprehensive error handling

**Intelligent Performance:**

- Cache-optimized dataset preparation eliminates redundant database calls
- Smart format detection with URL-based priority and intelligent fallback
- Thread-safe operations suitable for production multi-worker environments

**Robust Error Handling:**

- Graceful fallback to mock data when real data unavailable
- Comprehensive error recovery with automatic retry mechanisms
- Detailed logging and health scoring for operational monitoring

**Scalable Architecture:**

- Organized directory structure supports large datasets
- Configurable storage locations for environment-specific deployments
- Versioned dataset management with automatic cache invalidation

### Machine Learning Best Practices

**Proper Data Separation:**

- Correct handling of train/validation/test splits from database
- Smart validation data selection with test data protection
- Final evaluation on truly held-out test data when validation available

**Comprehensive Evaluation:**

- Unbiased performance assessment on unseen data
- Complete classification metrics (accuracy, precision, recall, F1-score)
- Professional logging with detailed results presentation

**Model Compatibility:**

- Universal support for all architectures (CNN, ResNet, DenseNet, MobileNet, ViT)
- Automatic class detection and model output validation
- Consistent label artifacts for training-inference compatibility

## Usage Examples

### Basic Training Data Flow

```python
from datasets.data_loading_service import DataLoadingService
from database.services.training_data_service import TrainingDataService

# Get training data with cache optimization
training_data = await TrainingDataService.get_training_data(model_run_uuid)

# Create optimized data loaders
data_loaders = await DataLoadingService.create_data_loaders_with_cache_optimization(
    training_data=training_data,
    augmentations=augmentations
)

# Access individual loaders
train_loader = data_loaders["train"]
test_loader = data_loaders["test"]
validation_loader = data_loaders.get("validation")  # Optional
```

### Direct Dataset Usage

```python
from datasets.image_dataset import ImageDataset
from config.paths import get_dataset_images_dir

# Create dataset from dataset_sets
images_dir = get_dataset_images_dir(dataset_uuid, content_updated_at)
dataset = ImageDataset(dataset_sets, images_dir)

# Get dataset information
info = dataset.get_dataset_info()
print(f"Dataset: {info['num_samples']} images, {info['num_coin_sides']} classes")

# Validate dataset health
validation_results = dataset.validate_dataset_formats()
print(f"Health score: {validation_results['health_score']}")
```

### Cache Management

```python
from database.services.dataset_state_cache import DatasetStateCacheService

# Check cache validity
is_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, content_updated_at)

# Get cache statistics
stats = DatasetStateCacheService.get_cache_stats()
print(f"Cache: {stats['total_entries']} total, {stats['prepared_entries']} prepared")
```

## Testing and Validation

### Comprehensive Test Coverage

The training data flow is thoroughly tested across multiple levels:

**Unit Tests:**

- Dataset cache invalidation system (14 tests)
- Training data service cache integration (12 tests)
- Image dataset multi-format loading (18 tests)
- Data loading service functionality (10 tests)

**Integration Tests:**

- End-to-end data flow integration (6 tests)
- Cache-optimized data loading (8 tests)
- Multi-format dataset creation and loading
- Real image loading with set type handling

**Manual Testing:**

```bash
# Run cache invalidation demo
python tests/manual/simple_cache_demo.py

# Run comprehensive test suite
python -m pytest tests/database/services/test_dataset_state_cache.py \
                 tests/database/services/test_training_data_service_cache.py \
                 tests/integration/test_data_loading_cache_integration.py -v
```

### Validation Features

**Dataset Health Assessment:**

- Quantitative health scoring (0.0-1.0 scale)
- Format distribution analysis
- Corrupted file detection
- Large file identification
- Actionable recommendations for improvements

**Performance Monitoring:**

- Format-specific loading time tracking
- Success rate analysis by image format
- Memory usage validation
- Thread-safe performance metrics collection

## Training Workflow Implementation

### Proper Machine Learning Data Separation

The training data flow implements correct ML practices for data separation:

**Training Phase Logic:**

- **Training Data**: Used for model parameter updates with augmentations
- **Validation Data**: Used for epoch validation when available (deterministic transforms)
- **Test Data**: Used for final evaluation only when validation data exists, otherwise used for epoch validation

**Smart Data Selection:**

```python
# Intelligent validation data selection during training
validation_loader = self.data_loaders.get("validation", self.data_loaders["test"])
loader_type = "validation" if "validation" in self.data_loaders else "test"
```

**Final Evaluation Logic:**

- **With Validation Data**: Final test evaluation on truly held-out test data
- **Without Validation Data**: No final evaluation (test data was used during training)

### Complete Training Flow

1. **Data Preparation**: Cache-optimized dataset metadata retrieval
2. **Image Acquisition**: Multi-format image download and organization
3. **Data Loading**: PyTorch DataLoader creation with proper set separation
4. **Training Execution**: Model training with smart validation data usage
5. **Final Evaluation**: Comprehensive metrics on held-out test data (when applicable)

### Training Benefits

**Machine Learning Best Practices:**

- ✅ Proper train/validation/test separation
- ✅ Unbiased evaluation on truly held-out data
- ✅ Smart logic prevents data leakage
- ✅ Comprehensive classification metrics
- ✅ Professional logging and results presentation

**Production Readiness:**

- ✅ Backward compatibility with existing workflows
- ✅ Test data protection when validation data exists
- ✅ Comprehensive error handling and recovery
- ✅ Thread-safe operations for multi-worker environments

## Future Enhancements

The training data flow provides a solid foundation for future improvements:

**Performance Optimizations:**

- External cache storage (Redis integration)
- Parallel image loading with multi-threading
- Advanced image preprocessing pipelines
- Automatic dataset version cleanup

**Enhanced Monitoring:**

- Real-time performance metrics export
- Advanced dataset quality assessment
- Automated dataset optimization recommendations
- Integration with monitoring systems (Prometheus, Grafana)

**Advanced Features:**

- Intelligent format conversion during preprocessing
- AI-driven format selection based on use case
- Advanced metadata handling and extraction
- Format-aware augmentation strategies

## Summary

The training data flow represents a complete transformation from mock data generation to a production-ready, real image training pipeline. The system provides:

**Complete Process Coverage:**

- Database metadata retrieval with intelligent caching
- Multi-format image acquisition and organization
- PyTorch data loader creation with proper ML practices
- Training execution with comprehensive evaluation

**Production-Ready Features:**

- Robust error handling and recovery mechanisms
- Thread-safe operations for multi-worker environments
- Configurable storage locations for different deployment environments
- Comprehensive testing coverage across all components

**Machine Learning Best Practices:**

- Proper train/validation/test data separation
- Unbiased evaluation on held-out test data
- Smart data selection logic to prevent data leakage
- Universal model architecture compatibility

This implementation successfully enables the coin classification system to train on real-world data while maintaining the highest standards of machine learning methodology and production reliability.